import numpy as np
import pandas as pd
from scipy import stats
import talib as ta
import statsmodels.api as sm
from typing import Dict, Any, Optional, Tuple
import warnings
from dataclasses import dataclass

# 抑制不必要的警告
warnings.filterwarnings('ignore', category=RuntimeWarning)

@dataclass
class AnalysisConfig:
    """分析配置类，集中管理所有参数"""
    # 数据列名配置
    date_column: str = 'date'
    close_column: str = 'close'
    high_column: str = 'high'
    low_column: str = 'low'
    volume_column: str = 'volume'
    open_column: str = 'open'
    amount_column: str = 'amount'

    # 技术指标参数
    rsi_period: int = 14
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    ma_short: int = 5
    ma_medium: int = 10
    ma_long: int = 20
    ma_extra_long: int = 30

    # 阈值参数
    volume_threshold: float = 1.5  # 放量阈值
    momentum_threshold: float = 0.002  # 动量阈值
    rsi_oversold: float = 30  # RSI超卖线
    rsi_overbought: float = 70  # RSI超买线

    # 买入信号权重
    signal_weights: Dict[str, float] = None

    def __post_init__(self):
        if self.signal_weights is None:
            self.signal_weights = {
                'strong_trend': 0.3,
                'breakout_platform': 0.25,
                'big_divergence': 0.2,
                'fund_accumulation': 0.25
            }

class StockOldAnalyzer:
    """优化的股票分析器类，提供多种技术分析和量化指标计算功能

    主要优化：
    1. 参数化配置，提高灵活性
    2. 改进异常处理和数据验证
    3. 优化算法逻辑，提高准确度
    4. 增强买卖点识别的胜率
    """

    def __init__(self, data: Optional[pd.DataFrame], config: Optional[Dict[str, Any]] = None):
        """初始化StockOldAnalyzer实例

        Args:
            data: 股票数据DataFrame
            config: 配置字典，可指定列名等参数
        """
        self.data = data
        self.config = AnalysisConfig(**config) if config else AnalysisConfig()
        self._validate_data()

        # 向后兼容：保留旧的属性名
        self.date_column = self.config.date_column
        self.close_column = self.config.close_column
        self.high_column = self.config.high_column
        self.low_column = self.config.low_column
        self.volume_column = self.config.volume_column
        self.open_column = self.config.open_column
        self.amount_column = self.config.amount_column

        # 缓存计算结果，避免重复计算
        self._cache = {}

    def _validate_data(self) -> None:
        """验证输入数据的有效性"""
        if self.data is None:
            return

        if not isinstance(self.data, pd.DataFrame):
            raise ValueError("数据必须是pandas DataFrame格式")

        required_columns = [
            self.config.close_column,
            self.config.high_column,
            self.config.low_column,
            self.config.volume_column,
            self.config.open_column
        ]

        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的数据列: {missing_columns}")

        if len(self.data) < 30:
            warnings.warn("数据量不足30条，可能影响分析准确性", UserWarning)

    def _get_cached_or_compute(self, key: str, compute_func) -> Any:
        """获取缓存结果或计算新结果"""
        if key not in self._cache:
            self._cache[key] = compute_func()
        return self._cache[key]

    def _cal_limit_num(self) -> int:
        """计算股票连板数量（涨停板连续天数）

        优化：
        1. 更精确的涨停判断逻辑
        2. 考虑价格精度问题
        3. 增加数据验证

        Returns:
            连板数量
        """
        if self.data is None or len(self.data) < 1:
            return 0

        try:
            limit = 0
            close_col = self.config.close_column
            high_col = self.config.high_column
            low_col = self.config.low_column
            open_col = self.config.open_column

            for i in range(1, min(10, len(self.data))):
                today = self.data.iloc[-i]

                # 更精确的涨停判断：收盘价等于最高价，且涨幅接近10%
                is_limit_up = (
                    abs(today[close_col] - today[high_col]) < today[close_col] * 0.001 and  # 收盘价接近最高价
                    today[high_col] == today[low_col]  # 一字涨停
                ) or (
                    abs(today[close_col] - today[high_col]) < today[close_col] * 0.001 and  # 收盘价接近最高价
                    i < len(self.data) - 1 and  # 确保有前一天数据
                    (today[close_col] / self.data.iloc[-i-1][close_col] - 1) > 0.095  # 涨幅接近10%
                )

                if is_limit_up:
                    limit += 1
                else:
                    break

            return limit
        except Exception as e:
            warnings.warn(f"计算连板数失败: {e}")
            return 0

    def _cal_price_momentum(self, period: int) -> float:
        """计算指定周期的价格动量

        优化：
        1. 增加参数验证
        2. 处理除零错误
        3. 使用对数收益率提高精度

        Args:
            period: 计算周期

        Returns:
            价格动量值（对数收益率）
        """
        if self.data is None or period <= 0 or len(self.data) < period + 1:
            return 0.0

        try:
            close_col = self.config.close_column
            current_price = self.data[close_col].iloc[-1]
            past_price = self.data[close_col].iloc[-period-1]

            if past_price <= 0:
                return 0.0

            # 使用对数收益率，更适合金融数据
            return np.log(current_price / past_price)
        except Exception as e:
            warnings.warn(f"计算{period}日价格动量失败: {e}")
            return 0.0

    def _cal_bottom_return(self) -> float:
        """计算从底部到当前的收益率

        优化：
        1. 动态调整观察期
        2. 使用更稳健的底部识别方法

        Returns:
            底部收益率
        """
        if self.data is None or len(self.data) < 20:
            return 0.0

        try:
            close_col = self.config.close_column
            low_col = self.config.low_column

            # 动态调整观察期，但不超过数据长度
            lookback_period = min(100, len(self.data))

            # 使用低点的滚动最小值作为底部
            min_price = self.data[low_col].rolling(lookback_period).min().iloc[-1]
            current_price = self.data[close_col].iloc[-1]

            if min_price <= 0:
                return 0.0

            return (current_price - min_price) / min_price
        except Exception as e:
            warnings.warn(f"计算底部收益率失败: {e}")
            return 0.0

    def _cal_momentum(self) -> bool:
        """判断股票动量趋势

        优化：
        1. 增加多个时间周期的动量确认
        2. 考虑动量的持续性
        3. 增加量价配合验证

        Returns:
            动量是否为正且呈加速趋势
        """
        if self.data is None or len(self.data) < 21:
            return False

        try:
            # 计算多个周期的动量
            ret5 = self._cal_price_momentum(5)
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)

            # 动量递增且都为正
            momentum_increasing = ret5 > ret10 > ret20 > 0

            # 检查成交量配合
            volume_support = self._check_volume_momentum()

            return momentum_increasing and volume_support
        except Exception as e:
            warnings.warn(f"计算动量失败: {e}")
            return False

    def _check_volume_momentum(self) -> bool:
        """检查成交量是否支持价格动量"""
        try:
            volume_col = self.config.volume_column
            if len(self.data) < 10:
                return True  # 数据不足时不作为否决条件

            recent_volume = self.data[volume_col].iloc[-5:].mean()
            prev_volume = self.data[volume_col].iloc[-10:-5].mean()

            return recent_volume > prev_volume * 1.1  # 成交量温和放大
        except Exception:
            return True

    def _cal_decision_date_return(self) -> float:
        """计算决策日到当前的收益率

        优化：
        1. 动态调整决策日期
        2. 考虑交易日历

        Returns:
            决策日收益率
        """
        if self.data is None or len(self.data) < 5:
            return 0.0

        try:
            close_col = self.config.close_column
            # 动态决策日：5天前或数据长度的1/10，取较小值
            decision_days = min(5, max(1, len(self.data) // 10))

            decision_price = self.data[close_col].iloc[-decision_days-1]
            current_price = self.data[close_col].iloc[-1]

            if decision_price <= 0:
                return 0.0

            return (current_price - decision_price) / decision_price
        except Exception as e:
            warnings.warn(f"计算决策日收益率失败: {e}")
            return 0.0

    def _cal_isTup(self) -> bool:
        """判断是否出现三连阳形态

        优化：
        1. 增加阳线强度判断
        2. 考虑成交量配合
        3. 避免假阳线

        Returns:
            是否三连阳
        """
        if self.data is None or len(self.data) < 3:
            return False

        try:
            close_col = self.config.close_column
            open_col = self.config.open_column
            high_col = self.config.high_column
            low_col = self.config.low_column

            consecutive_up_days = 0
            total_gain = 0.0

            for i in range(1, 4):  # 检查最近3天
                day = self.data.iloc[-i]

                # 阳线判断：收盘价高于开盘价
                is_up_day = day[close_col] > day[open_col]

                if is_up_day:
                    # 计算实体强度（实体占总振幅的比例）
                    total_range = day[high_col] - day[low_col]
                    body_size = day[close_col] - day[open_col]

                    if total_range > 0:
                        body_ratio = body_size / total_range
                        # 要求实体占比超过30%，避免十字星等弱势形态
                        if body_ratio > 0.3:
                            consecutive_up_days += 1
                            total_gain += (day[close_col] - day[open_col]) / day[open_col]
                        else:
                            break
                    else:
                        break
                else:
                    break

            # 三连阳且总涨幅合理（避免过度拉升）
            return consecutive_up_days == 3 and 0.02 < total_gain < 0.15
        except Exception as e:
            warnings.warn(f"计算三连阳失败: {e}")
            return False

    def _cal_emv(self) -> bool:
        """计算优化的EMV（能量潮）指标

        优化：
        1. 改进EMV计算公式
        2. 增加多周期确认
        3. 考虑成交量异常值处理

        Returns:
            EMV是否显示买入信号
        """
        if self.data is None or len(self.data) < 20:
            return False

        try:
            high_col = self.config.high_column
            low_col = self.config.low_column
            volume_col = self.config.volume_column

            high = self.data[high_col]
            low = self.data[low_col]
            volume = self.data[volume_col]

            # 计算价格中点移动
            mid_point = (high + low) / 2
            distance_move = mid_point.diff()

            # 计算高低价差，避免除零
            price_range = high - low
            price_range = price_range.replace(0, price_range.mean())

            # 改进的EMV计算，考虑成交量的对数变换以减少异常值影响
            log_volume = np.log1p(volume)  # log(1+x)避免log(0)
            emv = distance_move * log_volume / price_range

            # 多周期EMV均线
            emv_short = emv.rolling(7).mean()
            emv_long = emv.rolling(14).mean()

            # 信号确认：短期EMV上穿长期EMV且都为正
            current_emv = emv.iloc[-1]
            current_short = emv_short.iloc[-1]
            current_long = emv_long.iloc[-1]
            prev_short = emv_short.iloc[-2] if len(emv_short) > 1 else 0
            prev_long = emv_long.iloc[-2] if len(emv_long) > 1 else 0

            # EMV金叉且趋势向上
            golden_cross = (current_short > current_long and
                          prev_short <= prev_long)
            positive_trend = current_emv > 0 and current_short > 0

            return golden_cross and positive_trend
        except Exception as e:
            warnings.warn(f"计算EMV失败: {e}")
            return False

    def _cal_k(self) -> str:
        """判断当前K线形态

        优化：
        1. 更精确的K线分类
        2. 考虑影线长度
        3. 增加特殊形态识别

        Returns:
            K线形态描述
        """
        if self.data is None or len(self.data) < 1:
            return ''

        try:
            today = self.data.iloc[-1]
            close_col = self.config.close_column
            open_col = self.config.open_column
            high_col = self.config.high_column
            low_col = self.config.low_column

            close_price = today[close_col]
            open_price = today[open_col]
            high_price = today[high_col]
            low_price = today[low_col]

            # 计算实体和影线
            body_size = abs(close_price - open_price)
            total_range = high_price - low_price
            upper_shadow = high_price - max(close_price, open_price)
            lower_shadow = min(close_price, open_price) - low_price

            if total_range == 0:
                return '一字线'

            # 计算比例
            body_ratio = body_size / total_range
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range

            # 十字星判断（实体很小）
            if body_ratio < 0.1:
                if upper_shadow_ratio > 0.4 and lower_shadow_ratio > 0.4:
                    return '十字星'
                elif upper_shadow_ratio > 0.6:
                    return '倒锤头'
                elif lower_shadow_ratio > 0.6:
                    return '锤头'
                else:
                    return '小十字'

            # 阳线
            if close_price > open_price:
                if body_ratio > 0.7:
                    return '大阳线'
                elif body_ratio > 0.4:
                    return '中阳线'
                else:
                    if upper_shadow_ratio > 0.3:
                        return '上影阳线'
                    elif lower_shadow_ratio > 0.3:
                        return '下影阳线'
                    else:
                        return '小阳线'

            # 阴线
            elif close_price < open_price:
                if body_ratio > 0.7:
                    return '大阴线'
                elif body_ratio > 0.4:
                    return '中阴线'
                else:
                    if upper_shadow_ratio > 0.3:
                        return '上影阴线'
                    elif lower_shadow_ratio > 0.3:
                        return '下影阴线'
                    else:
                        return '小阴线'

            return '平盘'
        except Exception as e:
            warnings.warn(f"计算K线形态失败: {e}")
            return ''

    def analyze_stock(self, code: str, df: Optional[pd.DataFrame] = None) -> 'StockOldAnalysis':
        """分析股票并返回StockOldAnalysis对象

        优化：
        1. 增加数据验证和错误处理
        2. 优化计算顺序，避免重复计算
        3. 改进评分算法
        4. 增加缓存机制

        Args:
            code: 股票代码
            df: 股票数据DataFrame，如不提供则使用实例数据

        Returns:
            包含股票分析结果的对象
        """
        try:
            try:
                from .data_models import StockOldAnalysis
            except ImportError:
                from data_models import StockOldAnalysis

            # 数据准备和验证
            if df is not None:
                self.data = df
                self._validate_data()

            if self.data is None or len(self.data) < 10:
                return self._create_empty_analysis(code)

            # 清空缓存，准备新的计算
            self._cache.clear()

            # 基础指标计算（按依赖关系排序）
            limit = self._cal_limit_num()
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            ret100 = self._cal_price_momentum(100)
            decisionPercent = self._cal_decision_date_return()

            # 技术形态指标
            momentum = self._cal_momentum()
            isTup = self._cal_isTup()
            kline = self._cal_k()

            # 复杂技术指标
            isBottomInversion = self._cal_bottom_inversion()
            isHeavyVolume = self._cal_isHeavyVolume()
            macd = self._cal_macd()
            ma = self._cal_ma()
            max30 = self._cal_high_max()
            emv = self._cal_emv()

            # 高级量化信号
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()
            isFundAccumulation = self._cal_fund_accumulation()

            # CAPM模型计算
            capm_result = self._cal_CAPM()

            # 综合买入信号（优化后的算法）
            buySignal = self._cal_buy_signal_enhanced(
                isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation,
                momentum, macd, ma, emv
            )

            # 计算优化的评分
            scores = self._calculate_comprehensive_scores(
                momentum, isBottomInversion, isHeavyVolume, macd, ma, emv,
                isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation
            )

            # 创建并返回StockOldAnalysis对象
            return StockOldAnalysis(
                code=code,
                limit=limit,
                ret10=round(ret10, 4),
                ret20=round(ret20, 4),
                ret100=round(ret100, 4),
                momentum=momentum,
                isBottomInversion=isBottomInversion,
                decisionPercent=round(decisionPercent, 4),
                isTup=isTup,
                isHeavyVolume=isHeavyVolume,
                macd=macd,
                kline=kline,
                ret=round(capm_result.get('ret', 0), 4),
                md=round(capm_result.get('max_drawdown', 0), 4),
                alpha=round(capm_result.get('alpha', 0), 4),
                beta=round(capm_result.get('beta', 1), 4),
                emv=emv,
                score1=scores['fundamental'],
                score2=scores['technical'],
                score3=scores['liquidity'],
                score4=scores['sentiment'],
                ma=ma,
                max30=max30,
                strongTrend=isStrongTrend,
                breakoutPlatform=isBreakoutPlatform,
                bigDivergence=isBigDivergence,
                fundAccumulation=isFundAccumulation,
                buySignal=buySignal
            )
        except Exception as e:
            warnings.warn(f"分析股票 {code} 失败: {e}")
            return self._create_empty_analysis(code)

    def _create_empty_analysis(self, code: str) -> 'StockOldAnalysis':
        """创建空的分析结果对象"""
        try:
            from .data_models import StockOldAnalysis
        except ImportError:
            from data_models import StockOldAnalysis
        return StockOldAnalysis(
            code=code,
            limit=0,
            ret10=0,
            ret20=0,
            ret100=0,
            momentum=False,
            isBottomInversion=False,
            decisionPercent=0,
            isTup=False,
            isHeavyVolume=False,
            macd=False,
            kline='',
            ret=0,
            md=0,
            alpha=0,
            beta=1,
            emv=False,
            score1=0,
            score2=0,
            score3=0,
            score4=0,
            ma=False,
            max30=False,
            strongTrend=False,
            breakoutPlatform=False,
            bigDivergence=False,
            fundAccumulation=False,
            buySignal=False
        )

    def _calculate_comprehensive_scores(self, momentum: bool, isBottomInversion: bool,
                                      isHeavyVolume: bool, macd: bool, ma: bool, emv: bool,
                                      isStrongTrend: bool, isBreakoutPlatform: bool,
                                      isBigDivergence: bool, isFundAccumulation: bool) -> Dict[str, float]:
        """计算综合评分

        Args:
            各种技术指标的布尔值

        Returns:
            包含四个维度评分的字典
        """
        # 技术面评分 (0-100)
        technical_signals = [momentum, macd, ma, emv, isStrongTrend]
        technical_score = sum(technical_signals) / len(technical_signals) * 100

        # 基本面评分 (暂时基于价格动量)
        ret10 = self._cal_price_momentum(10)
        ret20 = self._cal_price_momentum(20)
        fundamental_score = min(100, max(0, (ret10 + ret20) * 500 + 50))

        # 资金面评分
        liquidity_signals = [isHeavyVolume, isFundAccumulation]
        liquidity_score = sum(liquidity_signals) / len(liquidity_signals) * 100

        # 风口面评分
        sentiment_signals = [isBottomInversion, isBreakoutPlatform, isBigDivergence]
        sentiment_score = sum(sentiment_signals) / len(sentiment_signals) * 100

        return {
            'technical': round(technical_score, 2),
            'fundamental': round(fundamental_score, 2),
            'liquidity': round(liquidity_score, 2),
            'sentiment': round(sentiment_score, 2)
        }
    def _cal_isHeavyVolume(self):
        """判断是否出现放量突破

        Returns:
            bool: 是否放量突破
        """
        try:
            # 实体占比
            body_ratio = np.abs(self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 涨幅
            price_change = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9)
            # 价格位置
            price_position = (self.data[self.close_column] - self.data[self.low_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 突破阻力
            resistance_break = self.data[self.close_column] > self.data[self.high_column].shift(1)

            # 综合判断
            is_heavy = ((body_ratio > 0.6) &
                        (price_change > 0.03) &
                        (price_position > 0.7) &
                        resistance_break &
                        (self.data[self.volume_column] > 1.5 * self.data[self.volume_column].rolling(20).mean())).iloc[-1]
            return bool(is_heavy)
        except Exception as e:
            warnings.warn(f"计算放量突破失败: {e}")
            return False

    def _cal_bottom_inversion(self):
        """判断是否出现底部反转信号

        Returns:
            bool: 是否底部反转
        """
        try:
            # 持续下跌判断 - 增加连续下跌天数要求
            consecutive_drop = 0
            max_consecutive_check = min(15, len(self.data))
            if max_consecutive_check > 1:
                for i in range(1, max_consecutive_check):
                    if self.data[self.close_column].iloc[-i] < self.data[self.close_column].iloc[-i-1]:
                        consecutive_drop += 1
                    else:
                        break

            # 放量大阳线
            big_candle = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9) > 0.05
            big_candle = big_candle.dropna()
            # 成交量放大
            volume_increase = self.data[self.volume_column] > 2.0 * self.data[self.volume_column].rolling(20).mean()
            volume_increase = volume_increase.dropna()
            # 均线信号
            ma5 = self.data[self.close_column].rolling(5).mean()
            ma10 = self.data[self.close_column].rolling(10).mean()
            # 对齐索引
            ma5, ma10 = ma5.align(ma10, join='inner')
            ma_cross = (ma5 > ma10) & (ma5.shift(1) <= ma10.shift(1))
            ma_cross = ma_cross.dropna()
            # RSI回升
            rsi = ta.RSI(self.data[self.close_column].values, timeperiod=14)
            rsi_rebound = False
            if len(rsi) >= 3:
                rsi_rebound = (rsi[-1] > rsi[-2]) & (rsi[-2] < rsi[-3]) & (rsi[-1] < 50)
            
            # 综合判断
            bottom_inversion = (consecutive_drop > 2)
            # 确保在访问iloc[-1]前有数据
            if len(big_candle) > 0:
                bottom_inversion &= big_candle.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有大阳线数据，不能确认底部反转
            
            if len(volume_increase) > 0:
                bottom_inversion &= volume_increase.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有成交量数据，不能确认底部反转
            
            # 增加均线交叉的严格要求
            if len(ma_cross) > 0:
                # 要求不仅金叉，还要ma5在ma10之上有一定幅度
                ma_cross_strength = (ma5.iloc[-1] - ma10.iloc[-1]) / ma10.iloc[-1] > 0.01
                bottom_inversion &= (ma_cross.iloc[-1] & ma_cross_strength)
            else:
                bottom_inversion = False
            
            # 保持RSI回升条件但作为附加条件
            bottom_inversion &= rsi_rebound
            
            return bool(bottom_inversion)
        except Exception as e:
            print(f"计算底部反转失败: {e}")
            return False

    def _max_drawdown(self):
        """计算最大回撤

        Returns:
            float: 最大回撤值
        """
        try:
            cumulative_return = (1 + self.data['daily_return']).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()
            return max_drawdown
        except Exception as e:
            print(f"计算最大回撤失败: {e}")
            return 0

    def _cal_high_max(self):
        """判断收盘价是否接近最高价

        Returns:
            bool: 收盘价是否接近最高价
        """
        try:
            # 检查收盘价是否接近最高价
            high_close_ratio = self.data[self.close_column] / self.data[self.high_column]
            # 确保只使用有值的行
            high_close_ratio = high_close_ratio.dropna()
            if len(high_close_ratio) == 0:
                return False
            is_high_max = (high_close_ratio > 0.98).iloc[-1]
            return bool(is_high_max)
        except Exception as e:
            print(f"计算高价最大值失败: {e}")
            return False

    def _cal_CAPM(self):
        """计算CAPM模型相关指标

        Returns:
            dict: 包含收益率、alpha、beta和最大回撤的字典
        """
        try:
            if len(self.data) < 30:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算收益率
            returns = np.log(self.data[self.close_column] / self.data[self.close_column].shift(1))
            returns = returns.dropna()

            # 假设市场收益率为自身收益率（实际应用中应使用大盘指数）
            market_returns = returns.copy()

            if len(returns) < 10:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算beta和alpha
            market_returns_add = sm.add_constant(market_returns)  # 增加常数列
            model = sm.OLS(endog=returns, exog=market_returns_add)  # 计算线性回归模型
            result = model.fit()  # 拟合

            # 计算平均日收益率
            ret = returns.mean()

            # 计算最大回撤
            cumulative_return = (1 + returns).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()

            # 年化alpha
            alpha = round(result.params.iloc[0] * 250, 4)
            beta = round(result.params.iloc[1], 4)

            return {
                'ret': ret,
                'alpha': alpha,
                'beta': beta,
                'max_drawdown': max_drawdown
            }
        except Exception as e:
            print(f"CAPM计算失败: {e}")
            return {
                'ret': 0,
                'alpha': 0,
                'beta': 1,
                'max_drawdown': 0
            }

    def _cal_macd(self) -> bool:
        """计算优化的MACD指标并判断买入信号

        优化：
        1. 改进底背离识别算法
        2. 增加多重确认机制
        3. 考虑成交量配合
        4. 优化参数设置

        Returns:
            是否出现MACD买入信号
        """
        if self.data is None or len(self.data) < 60:
            return False

        try:
            close_col = self.config.close_column
            close = self.data[close_col].astype(float).values

            # 使用配置的MACD参数
            dif, dea, hist = ta.MACD(
                close,
                fastperiod=self.config.macd_fast,
                slowperiod=self.config.macd_slow,
                signalperiod=self.config.macd_signal
            )

            # 数据清理和验证
            valid_indices = ~(np.isnan(dif) | np.isnan(dea) | np.isnan(hist))
            if np.sum(valid_indices) < 30:
                return False

            dif_clean = dif[valid_indices]
            dea_clean = dea[valid_indices]
            hist_clean = hist[valid_indices]
            close_clean = close[valid_indices]

            if len(dif_clean) < 30:
                return False

            # 优化的底背离识别
            def find_enhanced_bottom_divergence():
                """改进的底背离识别算法"""
                lookback_period = min(40, len(close_clean) - 10)
                if lookback_period < 20:
                    return False

                price_recent = close_clean[-lookback_period:]
                dif_recent = dif_clean[-lookback_period:]

                # 使用滚动窗口找低点，更稳健
                window_size = 5
                price_lows = []
                dif_lows = []

                for i in range(window_size, len(price_recent) - window_size):
                    # 价格低点：窗口内最低点
                    if price_recent[i] == min(price_recent[i-window_size:i+window_size+1]):
                        # 确保是显著低点
                        if i > 0 and price_recent[i] < price_recent[i-1] * 0.995:
                            price_lows.append((i, price_recent[i]))
                            dif_lows.append((i, dif_recent[i]))

                if len(price_lows) < 2:
                    return False

                # 分析最近的两个低点
                price_low1 = price_lows[-2]
                price_low2 = price_lows[-1]
                dif_low1 = dif_lows[-2]
                dif_low2 = dif_lows[-1]

                # 底背离条件：价格新低但MACD不创新低，且有一定幅度
                price_decline = (price_low2[1] - price_low1[1]) / price_low1[1]
                dif_improvement = (dif_low2[1] - dif_low1[1]) / abs(dif_low1[1]) if dif_low1[1] != 0 else 0

                return price_decline < -0.02 and dif_improvement > 0.1  # 价格跌2%以上，MACD改善10%以上

            # 优化的金叉信号检查
            def check_enhanced_golden_cross():
                """改进的金叉信号检查"""
                if len(dif_clean) < 5:
                    return False

                # 当前和前几天的MACD值
                current_dif = dif_clean[-1]
                current_dea = dea_clean[-1]
                prev_dif = dif_clean[-2]
                prev_dea = dea_clean[-2]

                # 金叉确认：DIF上穿DEA
                golden_cross = current_dif > current_dea and prev_dif <= prev_dea

                # 位置确认：在零轴下方或刚突破零轴
                position_ok = current_dif < 0.1  # 允许轻微突破零轴

                # 趋势确认：MACD柱状线改善
                hist_improving = len(hist_clean) >= 3 and hist_clean[-1] > hist_clean[-3]

                # 强度确认：金叉角度不能太小
                cross_strength = abs(current_dif - current_dea) > abs(prev_dif - prev_dea)

                return golden_cross and position_ok and hist_improving and cross_strength

            # 增强的成交量确认
            def check_enhanced_volume_confirmation():
                """改进的成交量确认"""
                try:
                    volume_col = self.config.volume_column
                    if len(self.data) < 15:
                        return True

                    # 多周期成交量分析
                    recent_3d = self.data[volume_col].iloc[-3:].mean()
                    recent_7d = self.data[volume_col].iloc[-7:].mean()
                    historical_20d = self.data[volume_col].iloc[-20:-7].mean()

                    if historical_20d <= 0:
                        return True

                    # 成交量温和放大（避免过度放量）
                    volume_ratio_3d = recent_3d / historical_20d
                    volume_ratio_7d = recent_7d / historical_20d

                    # 理想的成交量放大：1.2-2.5倍
                    return (1.2 <= volume_ratio_3d <= 2.5 and
                           1.1 <= volume_ratio_7d <= 2.0)
                except:
                    return True

            # 综合信号评估
            has_bottom_divergence = find_enhanced_bottom_divergence()
            has_golden_cross = check_enhanced_golden_cross()
            volume_confirmed = check_enhanced_volume_confirmation()

            # 多级信号确认策略
            if has_bottom_divergence and has_golden_cross and volume_confirmed:
                return True  # 最强信号：底背离+金叉+量能配合

            # 次级信号：金叉+量能配合（无底背离）
            if has_golden_cross and volume_confirmed:
                # 额外检查：确保不是假突破
                if len(hist_clean) >= 5:
                    # HIST连续改善
                    hist_trend = all(hist_clean[-i] >= hist_clean[-i-1] for i in range(1, 4))
                    if hist_trend:
                        return True

            # 保守信号：仅金叉但位置较好
            if has_golden_cross:
                # 在相对低位的金叉
                if current_dif < -0.05 and current_dea < -0.05:  # 深度超卖后的金叉
                    return True

            return False
        except Exception as e:
            warnings.warn(f"MACD计算失败: {e}")
            return False

    def _cal_ma(self) -> bool:
        """计算优化的均线信号

        优化：
        1. 动态均线周期选择
        2. 多重确认机制
        3. 趋势强度量化
        4. 假突破过滤

        Returns:
            是否满足均线强势条件
        """
        if self.data is None or len(self.data) < 35:
            return False

        try:
            close_col = self.config.close_column
            volume_col = self.config.volume_column
            # 使用配置的均线周期
            ma_short = close.rolling(self.config.ma_short).mean()
            ma_medium = close.rolling(self.config.ma_medium).mean()
            ma_long = close.rolling(self.config.ma_long).mean()
            ma_extra_long = close.rolling(self.config.ma_extra_long).mean()

            # 数据有效性检查
            if any(len(ma) < 10 for ma in [ma_short, ma_medium, ma_long, ma_extra_long]):
                return False

            # 获取最新和历史数据
            current_price = close.iloc[-1]
            current_ma_short = ma_short.iloc[-1]
            current_ma_medium = ma_medium.iloc[-1]
            current_ma_long = ma_long.iloc[-1]
            current_ma_extra_long = ma_extra_long.iloc[-1]

            prev_ma_short = ma_short.iloc[-2]
            prev_ma_medium = ma_medium.iloc[-2]
            prev_ma_long = ma_long.iloc[-2]

            # 条件1: 多头排列强度评估
            bullish_alignment = (current_price > current_ma_short >
                               current_ma_medium > current_ma_long > current_ma_extra_long)

            # 计算排列强度（均线间距）
            if current_ma_extra_long > 0:
                alignment_strength = (current_ma_short - current_ma_extra_long) / current_ma_extra_long
                strong_alignment = alignment_strength > 0.05  # 5%以上的均线展开
            else:
                strong_alignment = False

            # 条件2: 均线交叉信号（更严格的确认）
            cross_signals = []

            # 短期均线上穿中期均线
            if current_ma_short > current_ma_medium and prev_ma_short <= prev_ma_medium:
                cross_signals.append('short_cross_medium')

            # 中期均线上穿长期均线
            if current_ma_medium > current_ma_long and prev_ma_medium <= prev_ma_long:
                cross_signals.append('medium_cross_long')

            has_meaningful_cross = len(cross_signals) > 0

            # 条件3: 均线斜率和趋势强度
            def calculate_ma_slope(ma_series, periods=3):
                """计算均线斜率"""
                if len(ma_series) < periods + 1:
                    return 0
                return (ma_series.iloc[-1] - ma_series.iloc[-periods-1]) / ma_series.iloc[-periods-1]

            ma_short_slope = calculate_ma_slope(ma_short)
            ma_medium_slope = calculate_ma_slope(ma_medium)
            ma_long_slope = calculate_ma_slope(ma_long)

            # 均线斜率递增且都向上
            positive_slopes = (ma_short_slope > self.config.momentum_threshold and
                             ma_medium_slope > self.config.momentum_threshold * 0.5 and
                             ma_long_slope > 0)

            slope_acceleration = ma_short_slope > ma_medium_slope > ma_long_slope

            # 条件4: 价格相对位置优化
            price_above_ma = current_price > current_ma_short

            # 价格距离均线的合理性（避免过度偏离）
            if current_ma_short > 0:
                price_deviation = (current_price - current_ma_short) / current_ma_short
                reasonable_deviation = 0.001 < price_deviation < 0.06  # 0.1%-6%之间
            else:
                reasonable_deviation = False

            # 条件5: 增强的成交量分析
            volume_confirmation = self._analyze_volume_pattern()

            # 条件6: 趋势持续性验证
            trend_sustainability = self._check_trend_sustainability(close, ma_short, ma_medium)

            # 条件7: 假突破过滤
            false_breakout_filter = self._filter_false_breakout(close, ma_short)

            # 综合评分系统
            score = 0
            if bullish_alignment: score += 25
            if strong_alignment: score += 15
            if has_meaningful_cross: score += 20
            if positive_slopes: score += 15
            if slope_acceleration: score += 10
            if price_above_ma and reasonable_deviation: score += 10
            if volume_confirmation: score += 15
            if trend_sustainability: score += 10
            if false_breakout_filter: score += 10

            # 降低阈值，使信号更容易触发（从70降到50）
            return score >= 50
        except Exception as e:
            warnings.warn(f"计算均线信号失败: {e}")
            return False

    def _analyze_volume_pattern(self) -> bool:
        """分析成交量模式"""
        try:
            volume_col = self.config.volume_column
            if len(self.data) < 15:
                return True

            # 多周期成交量对比
            recent_5d = self.data[volume_col].iloc[-5:].mean()
            recent_10d = self.data[volume_col].iloc[-10:].mean()
            historical_20d = self.data[volume_col].iloc[-25:-5].mean()

            if historical_20d <= 0:
                return True

            # 成交量温和放大且持续
            volume_growth = recent_5d > recent_10d > historical_20d
            moderate_increase = 1.2 <= recent_5d / historical_20d <= 2.5

            return volume_growth and moderate_increase
        except:
            return True

    def _check_trend_sustainability(self, close, ma_short, ma_medium) -> bool:
        """检查趋势可持续性"""
        try:
            if len(close) < 20:
                return True

            # 检查最近的回调幅度
            recent_high = close.iloc[-10:].max()
            recent_low = close.iloc[-5:].min()
            current_price = close.iloc[-1]

            if recent_high > 0:
                pullback_ratio = (recent_high - recent_low) / recent_high
                # 回调幅度适中（不超过8%）
                moderate_pullback = pullback_ratio < 0.08

                # 当前价格接近高点
                near_high = current_price > recent_high * 0.95

                return moderate_pullback and near_high

            return True
        except:
            return True

    def _filter_false_breakout(self, close, ma_short) -> bool:
        """过滤假突破"""
        try:
            if len(close) < 10:
                return True

            # 检查突破的持续性
            days_above_ma = 0
            for i in range(1, min(6, len(close))):
                if close.iloc[-i] > ma_short.iloc[-i]:
                    days_above_ma += 1
                else:
                    break

            # 至少持续3天在均线上方
            return days_above_ma >= 3
        except:
            return True
            
    def _cal_strong_trend(self):
        """计算强势趋势指标

        判断股票是否处于强势趋势，核心依据为股价上涨趋势明显且量价强势配合
        - 上涨趋势：均线系统多头排列、价格持续高于均线、整体呈上升态势
        - 量价配合：上涨时成交量放大，下跌时成交量萎缩，量价关系健康

        Returns:
            bool: 是否处于强势趋势
        """
        try:
            if len(self.data) < 20:  # 调整最小数据要求
                return False

            # 计算5日均线和10日均线
            close = self.data[self.close_column].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()

            if len(ma5) < 15:
                return False

            # 条件1: 股价持续高于均线，上涨趋势明显
            # 最近10天股价在5日线之上的比例
            recent_days = 10
            above_ma5_count = 0

            for i in range(recent_days):
                idx = -(i + 1)
                if idx < -len(close):
                    break

                price = close.iloc[idx]
                ma5_value = ma5.iloc[idx]

                # 允许轻微跌破（不超过1%）
                if price >= ma5_value * 0.99:
                    above_ma5_count += 1

            # 至少90%的时间在5日线之上
            above_ma5_ratio = above_ma5_count / min(recent_days, len(close))
            if above_ma5_ratio < 0.90:
                return False

            # 条件2: 5日均线持续上升且斜率合理
            ma5_slope_periods = 8  # 增加观察期
            ma5_uptrend_count = 0
            slope_values = []

            for i in range(1, ma5_slope_periods + 1):
                if len(ma5) > i:
                    current_ma5 = ma5.iloc[-i]
                    prev_ma5 = ma5.iloc[-(i + 1)]
                    if current_ma5 > prev_ma5:
                        ma5_uptrend_count += 1
                        # 计算斜率（日涨幅）
                        slope = (current_ma5 - prev_ma5) / prev_ma5
                        slope_values.append(slope)

            # 5日线大部分时间上升（更严格）
            ma5_uptrend_ratio = ma5_uptrend_count / ma5_slope_periods
            if ma5_uptrend_ratio < 0.75:
                return False

            # 新增条件2.1: 上涨斜率要求
            if slope_values:
                avg_slope = sum(slope_values) / len(slope_values)
                # 5日均线日均涨幅要在0.3%-2%之间（避免过缓或过急）
                if avg_slope < 0.003 or avg_slope > 0.02:
                    return False

            # 新增条件2.2: 5日线相对10日线的位置
            if len(ma10) > 5:
                current_ma5 = ma5.iloc[-1]
                current_ma10 = ma10.iloc[-1]
                # 5日线要明显高于10日线
                if current_ma5 <= current_ma10 * 1.01:
                    return False

            # 条件3: 整体上升趋势确认（更严格）
            if len(close) > 15:
                current_price = close.iloc[-1]
                price_15_ago = close.iloc[-16]
                total_gain = (current_price - price_15_ago) / price_15_ago

                # 放宽涨幅要求：5%-50%之间（更宽松的范围）
                if total_gain < 0.05 or total_gain > 0.50:
                    return False

            # 新增条件3.1: 价格相对均线的强度
            current_price = close.iloc[-1]
            current_ma5 = ma5.iloc[-1]
            price_ma5_ratio = current_price / current_ma5
            # 价格要适度高于5日线，但不能过度偏离
            if price_ma5_ratio < 1.005 or price_ma5_ratio > 1.08:
                return False

            # 条件4: 量价强势配合
            volume_healthy = True
            try:
                # 获取最近5天和前5天的成交量
                recent_volume = self.data[self.volume_column].iloc[-5:].mean()
                prev_volume = self.data[self.volume_column].iloc[-10:-5].mean()
                long_avg_volume = self.data[self.volume_column].iloc[-20:].mean()

                # 成交量放大比例
                volume_ratio = recent_volume / prev_volume if prev_volume > 0 else 1
                long_volume_ratio = recent_volume / long_avg_volume if long_avg_volume > 0 else 1

                # 上涨时成交量放大，下跌时成交量萎缩
                up_days = 0
                up_volume_ratio = 1.0
                down_days = 0
                down_volume_ratio = 1.0

                for i in range(5):
                    idx = -(i + 1)
                    if idx < -len(close) or idx <= -len(close) + 1:
                        break

                    today_close = close.iloc[idx]
                    yesterday_close = close.iloc[idx - 1]
                    today_volume = self.data[self.volume_column].iloc[idx]
                    yesterday_volume = self.data[self.volume_column].iloc[idx - 1]

                    if today_close > yesterday_close:
                        up_days += 1
                        if yesterday_volume > 0:
                            up_volume_ratio *= (today_volume / yesterday_volume)
                    elif today_close < yesterday_close:
                        down_days += 1
                        if yesterday_volume > 0:
                            down_volume_ratio *= (today_volume / yesterday_volume)

                # 计算平均量比
                if up_days > 0:
                    up_volume_ratio = up_volume_ratio ** (1 / up_days)
                if down_days > 0:
                    down_volume_ratio = down_volume_ratio ** (1 / down_days)

                # 成交量健康条件: 上涨时量放大，下跌时量萎缩
                volume_healthy = (volume_ratio >= 1.2 and volume_ratio <= 3.0 and
                                long_volume_ratio >= 1.2 and long_volume_ratio <= 2.5 and
                                (up_days == 0 or up_volume_ratio >= 1.1) and
                                (down_days == 0 or down_volume_ratio <= 0.9))
            except:
                volume_healthy = False

            # 条件5: 避免超涨（更严格的RSI要求）
            rsi_ok = True
            try:
                rsi = ta.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI要在合理区间，避免超买也避免过弱
                    rsi_ok = 45 <= current_rsi <= 75
            except:
                rsi_ok = False

            # 条件6: 价格形态稳健
            price_stable = True
            try:
                if len(self.data) >= 8:
                    recent_highs = self.data[self.high_column].iloc[-8:]
                    recent_lows = self.data[self.low_column].iloc[-8:]
                    recent_closes = self.data[self.close_column].iloc[-8:]

                    # 检查是否有异常跳空（更严格）
                    for i in range(1, len(recent_highs)):
                        gap_up = recent_lows.iloc[i] > recent_highs.iloc[i-1] * 1.03
                        gap_down = recent_highs.iloc[i] < recent_lows.iloc[i-1] * 0.97

                        if gap_up or gap_down:
                            price_stable = False
                            break

                    # 新增：检查价格波动的稳定性
                    if price_stable:
                        daily_changes = []
                        for i in range(1, len(recent_closes)):
                            change = abs(recent_closes.iloc[i] - recent_closes.iloc[i-1]) / recent_closes.iloc[i-1]
                            daily_changes.append(change)

                        # 单日涨跌幅不能过大
                        max_daily_change = max(daily_changes) if daily_changes else 0
                        if max_daily_change > 0.08:  # 单日涨跌幅不超过8%
                            price_stable = False
            except:
                price_stable = False

            # 新增条件7: 趋势持续性验证
            trend_consistency = True
            try:
                if len(close) >= 20:
                    # 检查最近20天是否有明显的趋势一致性
                    ma5_20_ago = ma5.iloc[-20] if len(ma5) >= 20 else ma5.iloc[0]
                    current_ma5 = ma5.iloc[-1]

                    # 5日线20天总涨幅要合理
                    ma5_total_gain = (current_ma5 - ma5_20_ago) / ma5_20_ago
                    if ma5_total_gain < 0.06 or ma5_total_gain > 0.30:  # 6%-30%区间
                        trend_consistency = False
            except:
                trend_consistency = False

            # 新增条件8: 相对强度验证
            relative_strength = True
            try:
                if len(close) >= 30:
                    # 计算相对于自身历史的强度
                    recent_avg = close.iloc[-10:].mean()
                    historical_avg = close.iloc[-30:-10].mean()

                    strength_ratio = recent_avg / historical_avg
                    # 最近表现要明显强于历史平均
                    if strength_ratio < 1.08:  # 至少强8%
                        relative_strength = False
            except:
                relative_strength = False

            # 重新检查基础条件（因为变量作用域问题）
            # 条件1: 股价持续高于均线
            above_ma5_count = 0
            for i in range(10):
                idx = -(i + 1)
                if idx < -len(close):
                    break
                price = close.iloc[idx]
                ma5_value = ma5.iloc[idx]
                if price >= ma5_value * 0.99:
                    above_ma5_count += 1
            above_ma5_ratio = above_ma5_count / min(10, len(close))
            condition1_ok = above_ma5_ratio >= 0.90

            # 条件2: 5日均线持续上升
            ma5_uptrend_count = 0
            slope_values = []
            for i in range(1, 9):
                if len(ma5) > i:
                    current_ma5 = ma5.iloc[-i]
                    prev_ma5 = ma5.iloc[-(i + 1)]
                    if current_ma5 > prev_ma5:
                        ma5_uptrend_count += 1
                        slope = (current_ma5 - prev_ma5) / prev_ma5
                        slope_values.append(slope)

            ma5_uptrend_ratio = ma5_uptrend_count / 8
            avg_slope = sum(slope_values) / len(slope_values) if slope_values else 0
            condition2_ok = ma5_uptrend_ratio >= 0.75 and 0.003 <= avg_slope <= 0.02

            # 综合判断（放宽条件，提高实用性）
            basic_ok = condition1_ok and condition2_ok
            advanced_ok = volume_healthy and (rsi_ok or price_stable) and (trend_consistency or relative_strength)

            return basic_ok and advanced_ok
        except Exception as e:
            print(f"计算强势趋势股失败: {e}")
            return False
            
    def _cal_breakout_platform(self):
        """计算脱离平台指标

        识别股票是否突破平台，定义为: 至少有一个波峰和波谷，突破上一个波峰，成交量温和放大

        Returns:
            bool: 是否脱离平台
        """
        try:
            if len(self.data) < 20:
                return False

            close = self.data[self.close_column].astype(float)
            high = self.data[self.high_column].astype(float)
            low = self.data[self.low_column].astype(float)
            volume = self.data[self.volume_column].astype(float)

            # 1. 识别波峰和波谷
            # 使用滚动窗口识别波峰波谷
            window_size = 5
            if len(high) < window_size * 2 + 1:
                return False

            # 波峰: 窗口内的最大值
            peaks = high.rolling(window=window_size, center=True).apply(lambda x: x[window_size//2] == x.max(), raw=True)
            # 波谷: 窗口内的最小值
            valleys = low.rolling(window=window_size, center=True).apply(lambda x: x[window_size//2] == x.min(), raw=True)

            # 转换为布尔值
            peaks = peaks.astype(bool)
            valleys = valleys.astype(bool)

            # 至少有一个波峰和一个波谷
            if not (peaks.any() and valleys.any()):
                return False

            # 2. 找到最近的波峰
            # 获取最近的波峰索引
            peak_indices = peaks[peaks].index
            if len(peak_indices) == 0:
                return False
            last_peak_idx = peak_indices[-1]
            last_peak_price = high.loc[last_peak_idx]

            # 3. 检查是否突破上一个波峰
            current_price = close.iloc[-1]
            recent_high = high.iloc[-5:].max()  # 最近5天最高价

            # 突破定义: 收盘价或最近5天最高价突破上一个波峰
            breakthrough = (current_price > last_peak_price * 1.01) or (recent_high > last_peak_price)
            if not breakthrough:
                return False

            # 4. 成交量温和放大
            # 最近5天平均成交量 vs 突破前10天平均成交量
            if len(volume) < 15:
                return False

            recent_volume_avg = volume.iloc[-5:].mean()
            pre_break_volume_avg = volume.iloc[-15:-5].mean()

            if pre_break_volume_avg == 0:
                return False

            # 成交量放大1.2-3倍视为温和放大
            volume_ratio = recent_volume_avg / pre_break_volume_avg
            if not (1.2 <= volume_ratio <= 3.0):
                return False

            # 5. 突破有效性检查
            # 突破后价格没有大幅回落
            recent_low = low.iloc[-3:].min()
            no_major_pullback = recent_low > last_peak_price * 0.95
            if not no_major_pullback:
                return False

            # 条件6: 技术指标配合
            # MACD从底部向上
            macd_support = True
            try:
                dif, dea, hist = ta.MACD(close.values, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(hist) > 5:
                    recent_hist = hist[-5:]
                    # MACD柱状线从负值向正值发展
                    hist_improving = recent_hist[-1] > recent_hist[-3]
                    macd_support = hist_improving
            except:
                pass

            # 条件7: 相对强度
            # 相对于大盘的表现
            relative_strength = True
            try:
                if len(close) > 10:
                    stock_return = (current_price - close.iloc[-11]) / close.iloc[-11]
                    # 这里简化处理，实际应该对比大盘指数
                    # 要求股票表现不能太差
                    relative_strength = stock_return > -0.1
            except:
                pass

            return macd_support and relative_strength
        except Exception as e:
            print(f"计算脱离底部平台失败: {e}")
            return False
            
    def _cal_big_divergence(self):
        """计算大分歧形态

        识别股票是否出现大分歧形态，严格定义为连续两天长上下影且一上一下

        Returns:
            bool: 是否出现大分歧形态
        """
        try:
            if len(self.data) < 3:
                return False

            # 获取最近两天的数据
            day1 = self.data.iloc[-1]  # 最新一天
            day2 = self.data.iloc[-2]  # 前一天

            # 计算影线长度
            def calc_shadow_ratios(day_data):
                """计算上下影线比例"""
                total_range = day_data[self.high_column] - day_data[self.low_column]
                if total_range <= 0:
                    return 0, 0, 0

                body_size = abs(day_data[self.close_column] - day_data[self.open_column])
                upper_shadow = day_data[self.high_column] - max(day_data[self.open_column], day_data[self.close_column])
                lower_shadow = min(day_data[self.open_column], day_data[self.close_column]) - day_data[self.low_column]

                body_ratio = body_size / total_range
                upper_shadow_ratio = upper_shadow / total_range
                lower_shadow_ratio = lower_shadow / total_range

                return body_ratio, upper_shadow_ratio, lower_shadow_ratio

            # 计算两天的影线比例
            body1, upper1, lower1 = calc_shadow_ratios(day1)
            body2, upper2, lower2 = calc_shadow_ratios(day2)

            # 条件1: 两天都有明显的长影线
            # 影线长度至少占总振幅的30%
            day1_has_long_shadow = max(upper1, lower1) >= 0.3
            day2_has_long_shadow = max(upper2, lower2) >= 0.3

            if not (day1_has_long_shadow and day2_has_long_shadow):
                return False

            # 条件2: 分歧模式识别 - 严格的一上一下
            # 模式1: 先长上影线，后长下影线
            pattern1 = (upper2 >= 0.3 and upper2 > lower2 * 1.5 and  # 前一天长上影
                       lower1 >= 0.3 and lower1 > upper1 * 1.5)      # 当天长下影

            # 模式2: 先长下影线，后长上影线
            pattern2 = (lower2 >= 0.3 and lower2 > upper2 * 1.5 and  # 前一天长下影
                       upper1 >= 0.3 and upper1 > lower1 * 1.5)      # 当天长上影

            has_divergence_pattern = pattern1 or pattern2

            if not has_divergence_pattern:
                return False

            # 条件3: 成交量配合
            # 分歧期间成交量应该放大
            volume_amplified = True
            try:
                recent_volume = self.data[self.volume_column].iloc[-2:].mean()  # 两天平均成交量
                prev_volume = self.data[self.volume_column].iloc[-7:-2].mean()  # 前5天平均成交量

                if prev_volume > 0:
                    volume_ratio = recent_volume / prev_volume
                    volume_amplified = volume_ratio >= 1.2  # 成交量放大20%以上
            except:
                pass

            # 条件4: 价格位置判断
            # 判断是在相对高位还是低位发生分歧
            position_reasonable = True
            try:
                current_price = day1[self.close_column]

                # 计算20日内的价格位置
                if len(self.data) >= 20:
                    recent_20_high = self.data[self.high_column].iloc[-20:].max()
                    recent_20_low = self.data[self.low_column].iloc[-20:].min()
                    price_position = (current_price - recent_20_low) / (recent_20_high - recent_20_low)

                    # 在相对高位(>0.65)或低位(<0.35)的分歧更有意义
                    position_reasonable = price_position > 0.65 or price_position < 0.35
            except:
                pass

            # 条件5: 避免连续分歧
            # 前面不能有太多类似的分歧形态
            no_recent_divergence = True
            try:
                if len(self.data) >= 7:
                    # 检查前5天是否有类似的长影线
                    divergence_count = 0
                    for i in range(3, 7):
                        if i < len(self.data):
                            prev_day = self.data.iloc[-i]
                            _, prev_upper, prev_lower = calc_shadow_ratios(prev_day)
                            if max(prev_upper, prev_lower) >= 0.25:
                                divergence_count += 1
                                if divergence_count > 1:
                                    no_recent_divergence = False
                                    break
            except:
                pass

            # 条件6: 技术指标确认
            # RSI在超买或超卖区域更容易形成转折
            rsi_confirmation = True
            try:
                close = self.data[self.close_column].astype(float)
                rsi = ta.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI在35以下或65以上时分歧更有效
                    rsi_confirmation = current_rsi < 38 or current_rsi > 62
            except:
                pass

            return (volume_amplified or position_reasonable or
                   no_recent_divergence or rsi_confirmation)
        except Exception as e:
            print(f"计算大分歧失败: {e}")
            return False
            
    def _cal_fund_accumulation(self):
        """计算资金吸筹指标

        多维度判断股票是否有资金吸筹行为，包括成交量变化、价格重心、换手率等指标

        Returns:
            bool: 是否存在资金吸筹
        """
        try:
            # 1. 成交量变化检查
            def check_volume_change():
                # 成交量温和放大
                volume_trend = self.data[self.volume_column].rolling(5).mean()
                volume_increase = (volume_trend.iloc[-1] > volume_trend.iloc[-10] * 1.5) & (volume_trend.iloc[-1] < volume_trend.iloc[-10] * 3)
                return volume_increase
                
            # 2. 价格重心抬升
            def check_price_center():
                price_center = (self.data[self.high_column] + self.data[self.low_column]) / 2
                center_trend = price_center.rolling(5).mean()
                center_increase = center_trend.iloc[-1] > center_trend.iloc[-10]
                return center_increase
                
            # 3. 换手率适中
            def check_turnover():
                # 估算换手率（假设流通股不变）
                # 使用默认流通股数量，实际应用中应从配置或数据库获取
                circulating_shares = getattr(self.config, 'circulating_shares', 100000000)  # 1亿股默认值
                turnover = self.data[self.volume_column] / circulating_shares
                turnover_condition = (turnover.iloc[-1] > 0.02) & (turnover.iloc[-1] < 0.15)
                return turnover_condition
                
            # 4. 技术指标底部特征
            def check_technical_bottom():
                rsi = ta.RSI(self.data[self.close_column].values, timeperiod=14)
                macd, signal, hist = ta.MACD(self.data[self.close_column].values, fastperiod=12, slowperiod=26, signalperiod=9)
                technical_bottom = (rsi[-1] < 40) & (macd[-1] < 0) & (hist[-1] > hist[-2])
                return technical_bottom
                
            # 5. 价格位置合理
            def check_price_position():
                price_range = self.data[self.high_column].rolling(60).max() - self.data[self.low_column].rolling(60).min()
                price_position = (self.data[self.close_column].iloc[-1] - self.data[self.low_column].rolling(60).min().iloc[-1]) / (price_range.iloc[-1] + 1e-9)
                position_condition = (price_position > 0.2) & (price_position < 0.5)
                return position_condition
                
            # 6. 成交金额稳定增长
            def check_amount_growth():
                amount = self.data[self.volume_column] * self.data[self.close_column]
                amount_trend = amount.rolling(5).mean()
                amount_increase = (amount_trend.iloc[-1] > amount_trend.iloc[-10] * 1.3)
                return amount_increase
                
            # 综合判断（满足至少2个条件）
            conditions = [
                check_volume_change(),
                check_price_center(),
                check_turnover(),
                check_technical_bottom(),
                check_price_position(),
                check_amount_growth()
            ]
            
            met_conditions = sum(conditions)
            return met_conditions >= 2
        except Exception as e:
            print(f"计算资金吸筹失败: {e}")
            return False

    def _cal_buy_signal_enhanced(self, isStrongTrend: bool, isBreakoutPlatform: bool,
                               isBigDivergence: bool, isFundAccumulation: bool,
                               momentum: bool, macd: bool, ma: bool, emv: bool) -> bool:
        """计算增强的买入信号

        优化：
        1. 使用加权评分而非简单计数
        2. 增加风险控制条件
        3. 考虑市场环境因素
        4. 增加信号强度评估

        Args:
            各种技术指标的布尔值

        Returns:
            是否产生买入信号
        """
        try:
            # 主要信号权重配置
            signal_weights = self.config.signal_weights

            # 计算加权评分
            weighted_score = (
                signal_weights['strong_trend'] * isStrongTrend +
                signal_weights['breakout_platform'] * isBreakoutPlatform +
                signal_weights['big_divergence'] * isBigDivergence +
                signal_weights['fund_accumulation'] * isFundAccumulation
            )

            # 辅助信号确认
            auxiliary_signals = [momentum, macd, ma, emv]
            auxiliary_score = sum(auxiliary_signals) / len(auxiliary_signals)

            # 风险控制检查
            risk_checks = self._perform_risk_checks()

            # 市场环境评估
            market_condition = self._assess_market_condition()

            # 综合评分计算
            # 主要信号占70%，辅助信号占20%，市场环境占10%
            comprehensive_score = (
                weighted_score * 0.7 +
                auxiliary_score * 0.2 +
                market_condition * 0.1
            )

            # 买入信号条件（优化后更实用）：
            # 1. 综合评分超过阈值（降低阈值）
            # 2. 风险检查（作为参考，不作为硬性条件）
            # 3. 至少有一个主要信号为真
            signal_threshold = 0.4  # 降低阈值，提高信号敏感度
            has_main_signal = any([isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation])

            # 如果综合评分很高，即使风险检查不通过也可以买入
            high_confidence = comprehensive_score >= 0.7

            return ((comprehensive_score >= signal_threshold and has_main_signal) and
                   (risk_checks or high_confidence))
        except Exception as e:
            warnings.warn(f"计算买入信号失败: {e}")
            return False

    def _perform_risk_checks(self) -> bool:
        """执行风险控制检查"""
        try:
            if self.data is None or len(self.data) < 20:
                return False

            close_col = self.config.close_column
            high_col = self.config.high_column
            low_col = self.config.low_column
            volume_col = self.config.volume_column

            # 1. 价格位置检查 - 避免追高（放宽条件）
            recent_high = self.data[high_col].rolling(20).max().iloc[-1]
            recent_low = self.data[low_col].rolling(20).min().iloc[-1]
            current_price = self.data[close_col].iloc[-1]

            if recent_high > recent_low:
                price_position = (current_price - recent_low) / (recent_high - recent_low)
                # 放宽价格位置限制，允许在相对高位买入强势股
                if price_position > 0.95:  # 只有在极端高位才拒绝
                    return False

            # 2. 波动率检查 - 避免异常波动
            returns = self.data[close_col].pct_change().dropna()
            if len(returns) > 10:
                volatility = returns.rolling(10).std().iloc[-1]
                # 波动率不能过高
                if volatility > 0.08:  # 日波动率超过8%
                    return False

            # 3. 成交量异常检查
            recent_volume = self.data[volume_col].iloc[-5:].mean()
            historical_volume = self.data[volume_col].iloc[-20:-5].mean()

            if historical_volume > 0:
                volume_ratio = recent_volume / historical_volume
                # 成交量不能过度放大（可能是出货）
                if volume_ratio > 5.0:
                    return False

            # 4. RSI过热检查
            try:
                rsi = ta.RSI(self.data[close_col].values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    # RSI不能过度超买
                    if rsi[-1] > 80:
                        return False
            except:
                pass

            return True
        except Exception:
            return False

    def _assess_market_condition(self) -> float:
        """评估市场环境条件

        Returns:
            市场环境评分 (0-1)
        """
        try:
            if self.data is None or len(self.data) < 30:
                return 0.5  # 中性评分

            close_col = self.config.close_column
            volume_col = self.config.volume_column

            # 1. 趋势强度评估
            ma5 = self.data[close_col].rolling(5).mean()
            ma20 = self.data[close_col].rolling(20).mean()

            trend_score = 0.0
            if len(ma5) > 0 and len(ma20) > 0:
                if ma5.iloc[-1] > ma20.iloc[-1]:
                    trend_score = 0.3

            # 2. 成交量活跃度
            volume_score = 0.0
            recent_volume = self.data[volume_col].iloc[-10:].mean()
            historical_volume = self.data[volume_col].iloc[-30:-10].mean()

            if historical_volume > 0:
                volume_activity = recent_volume / historical_volume
                if 1.2 <= volume_activity <= 2.5:  # 适度活跃
                    volume_score = 0.3

            # 3. 价格稳定性
            stability_score = 0.0
            returns = self.data[close_col].pct_change().dropna()
            if len(returns) > 10:
                volatility = returns.rolling(10).std().iloc[-1]
                if 0.01 <= volatility <= 0.05:  # 适度波动
                    stability_score = 0.4

            return min(1.0, trend_score + volume_score + stability_score)
        except Exception:
            return 0.5