import numpy as np
import pandas as pd
from scipy import stats
import talib as ta
import statsmodels.api as sm

class StockOldAnalyzer:
    """股票分析器类，提供多种技术分析和量化指标计算功能"""
    def __init__(self, data, config=None):
        """初始化StockOldAnalyzer实例

        Args:
            data (pd.DataFrame): 股票数据DataFrame
            config (dict, optional): 配置字典，可指定列名等参数
        """
        self.data = data
        self.config = config or {}
        self.date_column = self.config.get('date_column', 'date')
        self.close_column = self.config.get('close_column', 'close')
        self.high_column = self.config.get('high_column', 'high')
        self.low_column = self.config.get('low_column', 'low')
        self.volume_column = self.config.get('volume_column', 'volume')
        self.open_column = self.config.get('open_column', 'open')
        # 新增成交额列定义
        self.amount_column = self.config.get('amount_column', 'amount')
        # 移除FastIndicatorCalculator引用，因为它在当前上下文中未定义
        # 如果需要使用该功能，应从正确的模块导入或重新实现
        self.fast_calculator = None

    def _cal_limit_num(self):
        """计算股票连板数量

        Returns:
            int: 连板数量
        """
        try:
            limit = 0
            for i in range(1, min(10, len(self.data))):
                today = self.data.iloc[-i]
                if today['high'] == today['low'] and today['close'] == today['high']:
                    limit += 1
                else:
                    break
            return limit
        except Exception as e:
            print(f"计算连板数失败: {e}")
            return 0

    def _cal_price_momentum(self, period):
        """计算指定周期的价格动量

        Args:
            period (int): 计算周期

        Returns:
            float: 价格动量值
        """
        try:
            if len(self.data) < period + 1:
                return 0
            return (self.data[self.close_column].iloc[-1] - self.data[self.close_column].iloc[-period-1]) / self.data[self.close_column].iloc[-period-1]
        except Exception as e:
            print(f"计算价格动量失败: {e}")
            return 0

    def _cal_bottom_return(self):
        """计算从底部到当前的收益率

        Returns:
            float: 底部收益率
        """
        try:
            min_price = self.data[self.low_column].rolling(100).min().iloc[-1]
            return (self.data[self.close_column].iloc[-1] - min_price) / min_price
        except Exception as e:
            print(f"计算底部收益率失败: {e}")
            return 0

    def _cal_momentum(self):
        """判断股票动量趋势

        Returns:
            bool: 动量是否为正且短期动量大于长期动量
        """
        try:
            # 计算近10日和近20日收益率
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            # 动量为正且10日动量大于20日动量
            return ret10 > 0 and ret20 > 0 and ret10 > ret20
        except Exception as e:
            print(f"计算动量失败: {e}")
            return False

    def _cal_decision_date_return(self):
        """计算决策日（5天前）到当前的收益率

        Returns:
            float: 决策日收益率
        """
        try:
            # 假设决策日为5天前
            if len(self.data) < 5:
                return 0
            decision_date_price = self.data[self.close_column].iloc[-5]
            current_price = self.data[self.close_column].iloc[-1]
            return (current_price - decision_date_price) / decision_date_price
        except Exception as e:
            print(f"计算决策日收益率失败: {e}")
            return 0

    def _cal_isTup(self):
        """判断是否出现三连阳形态

        Returns:
            bool: 是否三连阳
        """
        try:
            if len(self.data) < 3:
                return False
            # 三连阳判断
            for i in range(1, 4):
                day = self.data.iloc[-i]
                if day['close'] <= day['open']:
                    return False
            return True
        except Exception as e:
            print(f"计算三连阳失败: {e}")
            return False

    def _cal_emv(self):
        """计算简易版EMV（能量潮）指标

        Returns:
            bool: EMV是否大于其移动平均线且为正
        """
        try:
            # 简化版EMV计算
            if len(self.data) < 14:
                return False
            high = self.data[self.high_column]
            low = self.data[self.low_column]
            volume = self.data[self.volume_column]

            distance_move = (high + low) / 2 - (high.shift(1) + low.shift(1)) / 2
            box_ratio = volume / ((high - low) * 10000)
            emv = distance_move / box_ratio
            emv_ma = emv.rolling(14).mean()

            return emv.iloc[-1] > emv_ma.iloc[-1] and emv.iloc[-1] > 0
        except Exception as e:
            print(f"计算EMV失败: {e}")
            return False

    def _cal_k(self):
        """判断当前K线形态

        Returns:
            str: K线形态描述（大阳线、小阳线、大阴线、小阴线、十字星）
        """
        try:
            # 计算K线形态
            if len(self.data) < 2:
                return ''
            today = self.data.iloc[-1]
            yesterday = self.data.iloc[-2]

            # 简单分类K线形态
            if today['close'] > today['open']:
                if today['close'] - today['open'] > (today['high'] - today['low']) * 0.6:
                    return '大阳线'
                else:
                    return '小阳线'
            elif today['close'] < today['open']:
                if today['open'] - today['close'] > (today['high'] - today['low']) * 0.6:
                    return '大阴线'
                else:
                    return '小阴线'
            else:
                return '十字星'
        except Exception as e:
            print(f"计算K线形态失败: {e}")
            return ''

    def analyze_stock(self, code, df=None):
        """分析股票并返回StockOldAnalysis对象

        Args:
            code (str): 股票代码
            df (pd.DataFrame, optional): 股票数据DataFrame，如不提供则使用实例数据

        Returns:
            StockOldAnalysis: 包含股票分析结果的对象
        """
        """分析股票并返回StockOldAnalysis对象"""
        try:
            from .data_models import StockOldAnalysis
            
            # 如果提供了新数据，则使用新数据
            data = df if df is not None else self.data
            self.data = data
            
            # 计算各种指标
            limit = self._cal_limit_num()
            isBottomInversion = self._cal_bottom_inversion()
            isHeavyVolume = self._cal_isHeavyVolume()
            capm_result = self._cal_CAPM()
            macd = self._cal_macd()
            ma = self._cal_ma()
            max30 = self._cal_high_max()
            isTup = self._cal_isTup()
            emv = self._cal_emv()
            momentum = self._cal_momentum()
            decisionPercent = self._cal_decision_date_return()
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            ret100 = self._cal_price_momentum(100)
            kline = self._cal_k()
            # 新增四个量化信号
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()
            isFundAccumulation = self._cal_fund_accumulation()
            buySignal = self._cal_buy_signal()

            # 计算评分（这里假设需要实现评分逻辑）
            score1 = 0.0  # 使用量价强度作为第一个评分
            score2 = 0.0  # 示例值，需要根据实际逻辑计算
            score3 = 0.0  # 示例值，需要根据实际逻辑计算
            score4 = 0.0  # 示例值，需要根据实际逻辑计算

            # 创建并返回StockOldAnalysis对象
            return StockOldAnalysis(
                code=code,
                limit=limit,
                ret10=round(ret10, 4),
                ret20=round(ret20, 4),
                ret100=round(ret100, 4),
                momentum=momentum,
                isBottomInversion=isBottomInversion,
                decisionPercent=round(decisionPercent, 4),
                isTup=isTup,
                isHeavyVolume=isHeavyVolume,
                macd=macd,
                kline=kline,
                ret=round(capm_result.get('ret', 0), 4),
                md=round(capm_result.get('max_drawdown', 0), 4),
                alpha=round(capm_result.get('alpha', 0), 4),
                beta=round(capm_result.get('beta', 1), 4),
                emv=emv,
                score1=score1,
                score2=score2,
                score3=score3,
                score4=score4,
                ma=ma,
                max30=max30,
                strongTrend=isStrongTrend,
                breakoutPlatform=isBreakoutPlatform,
                bigDivergence=isBigDivergence,
                fundAccumulation=isFundAccumulation,
                buySignal=buySignal
            )
        except Exception as e:
            print(f"分析股票 {code} 失败: {e}")
            # 返回一个具有基本结构的StockOldAnalysis对象
            from .data_models import StockOldAnalysis
            return StockOldAnalysis(
                code=code,
                limit=0,
                ret10=0,
                ret20=0,
                ret100=0,
                momentum=False,
                isBottomInversion=False,
                decisionPercent=0,
                isTup=False,
                isHeavyVolume=False,
                macd=None,
                kline='',
                ret=0,
                md=0,
                alpha=0,
                beta=1,
                emv=False,
                score1=0,
                score2=0,
                score3=0,
                score4=0,
                ma=False,
                max30=False,
                strongTrend=False,
                breakoutPlatform=False,
                bigDivergence=False,
                volumePriceStrength=0,
                buySignal=False
            )
    def _cal_isHeavyVolume(self):
        """判断是否出现放量突破

        Returns:
            bool: 是否放量突破
        """
        try:
            # 实体占比
            body_ratio = np.abs(self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 涨幅
            price_change = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9)
            # 价格位置
            price_position = (self.data[self.close_column] - self.data[self.low_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 突破阻力
            resistance_break = self.data[self.close_column] > self.data[self.high_column].shift(1)
            
            # 综合判断
            is_heavy = ((body_ratio > 0.6) & 
                        (price_change > 0.03) & 
                        (price_position > 0.7) & 
                        resistance_break & 
                        (self.data[self.volume_column] > 1.5 * self.data[self.volume_column].rolling(20).mean())).iloc[-1]
            return bool(is_heavy)
        except Exception as e:
            print(f"计算放量突破失败: {e}")
            return False

    def _cal_bottom_inversion(self):
        """判断是否出现底部反转信号

        Returns:
            bool: 是否底部反转
        """
        try:
            # 持续下跌判断 - 增加连续下跌天数要求
            consecutive_drop = 0
            max_consecutive_check = min(15, len(self.data))
            if max_consecutive_check > 1:
                for i in range(1, max_consecutive_check):
                    if self.data[self.close_column].iloc[-i] < self.data[self.close_column].iloc[-i-1]:
                        consecutive_drop += 1
                    else:
                        break

            # 放量大阳线
            big_candle = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9) > 0.05
            big_candle = big_candle.dropna()
            # 成交量放大
            volume_increase = self.data[self.volume_column] > 2.0 * self.data[self.volume_column].rolling(20).mean()
            volume_increase = volume_increase.dropna()
            # 均线信号
            ma5 = self.data[self.close_column].rolling(5).mean()
            ma10 = self.data[self.close_column].rolling(10).mean()
            # 对齐索引
            ma5, ma10 = ma5.align(ma10, join='inner')
            ma_cross = (ma5 > ma10) & (ma5.shift(1) <= ma10.shift(1))
            ma_cross = ma_cross.dropna()
            # RSI回升
            rsi = ta.RSI(self.data[self.close_column].values, timeperiod=14)
            rsi_rebound = False
            if len(rsi) >= 3:
                rsi_rebound = (rsi[-1] > rsi[-2]) & (rsi[-2] < rsi[-3]) & (rsi[-1] < 50)
            
            # 综合判断
            bottom_inversion = (consecutive_drop > 2)
            # 确保在访问iloc[-1]前有数据
            if len(big_candle) > 0:
                bottom_inversion &= big_candle.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有大阳线数据，不能确认底部反转
            
            if len(volume_increase) > 0:
                bottom_inversion &= volume_increase.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有成交量数据，不能确认底部反转
            
            # 增加均线交叉的严格要求
            if len(ma_cross) > 0:
                # 要求不仅金叉，还要ma5在ma10之上有一定幅度
                ma_cross_strength = (ma5.iloc[-1] - ma10.iloc[-1]) / ma10.iloc[-1] > 0.01
                bottom_inversion &= (ma_cross.iloc[-1] & ma_cross_strength)
            else:
                bottom_inversion = False
            
            # 保持RSI回升条件但作为附加条件
            bottom_inversion &= rsi_rebound
            
            return bool(bottom_inversion)
        except Exception as e:
            print(f"计算底部反转失败: {e}")
            return False

    def _max_drawdown(self):
        """计算最大回撤

        Returns:
            float: 最大回撤值
        """
        try:
            cumulative_return = (1 + self.data['daily_return']).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()
            return max_drawdown
        except Exception as e:
            print(f"计算最大回撤失败: {e}")
            return 0

    def _cal_high_max(self):
        """判断收盘价是否接近最高价

        Returns:
            bool: 收盘价是否接近最高价
        """
        try:
            # 检查收盘价是否接近最高价
            high_close_ratio = self.data[self.close_column] / self.data[self.high_column]
            # 确保只使用有值的行
            high_close_ratio = high_close_ratio.dropna()
            if len(high_close_ratio) == 0:
                return False
            is_high_max = (high_close_ratio > 0.98).iloc[-1]
            return bool(is_high_max)
        except Exception as e:
            print(f"计算高价最大值失败: {e}")
            return False

    def _cal_CAPM(self):
        """计算CAPM模型相关指标

        Returns:
            dict: 包含收益率、alpha、beta和最大回撤的字典
        """
        try:
            if len(self.data) < 30:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算收益率
            returns = np.log(self.data[self.close_column] / self.data[self.close_column].shift(1))
            returns = returns.dropna()

            # 假设市场收益率为自身收益率（实际应用中应使用大盘指数）
            market_returns = returns.copy()

            if len(returns) < 10:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算beta和alpha
            market_returns_add = sm.add_constant(market_returns)  # 增加常数列
            model = sm.OLS(endog=returns, exog=market_returns_add)  # 计算线性回归模型
            result = model.fit()  # 拟合

            # 计算平均日收益率
            ret = returns.mean()

            # 计算最大回撤
            cumulative_return = (1 + returns).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()

            # 年化alpha
            alpha = round(result.params.iloc[0] * 250, 4)
            beta = round(result.params.iloc[1], 4)

            return {
                'ret': ret,
                'alpha': alpha,
                'beta': beta,
                'max_drawdown': max_drawdown
            }
        except Exception as e:
            print(f"CAPM计算失败: {e}")
            return {
                'ret': 0,
                'alpha': 0,
                'beta': 1,
                'max_drawdown': 0
            }

    def _cal_macd(self):
        """计算MACD指标并判断买入信号

        Returns:
            bool: 是否出现MACD买入信号
        """
        try:
            if len(self.data) < 60:
                return False

            close = self.data[self.close_column].astype(float).values
            dif, dea, hist = ta.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)

            # 去除NaN值
            valid_indices = ~(np.isnan(dif) | np.isnan(dea) | np.isnan(hist))
            if np.sum(valid_indices) < 30:
                return 0

            dif_clean = dif[valid_indices]
            dea_clean = dea[valid_indices]
            hist_clean = hist[valid_indices]
            close_clean = close[valid_indices]

            if len(dif_clean) < 30:
                return 0

            # 寻找底背离
            def find_bottom_divergence():
                # 寻找最近30天内的价格低点和MACD低点
                recent_period = 30
                if len(close_clean) < recent_period:
                    return False

                price_recent = close_clean[-recent_period:]
                dif_recent = dif_clean[-recent_period:]

                # 找到价格的两个低点
                price_lows = []
                dif_lows = []

                for i in range(5, len(price_recent) - 5):
                    # 价格低点：前后5天都比当天高
                    if (price_recent[i] == min(price_recent[i-5:i+6]) and
                        price_recent[i] < price_recent[i-1] and price_recent[i] < price_recent[i+1]):
                        price_lows.append((i, price_recent[i]))
                        dif_lows.append((i, dif_recent[i]))

                if len(price_lows) < 2:
                    return False

                # 取最近的两个低点
                price_low1 = price_lows[-2]
                price_low2 = price_lows[-1]
                dif_low1 = dif_lows[-2]
                dif_low2 = dif_lows[-1]

                # 底背离：价格创新低，但MACD不创新低
                price_divergence = price_low2[1] < price_low1[1]  # 价格新低
                macd_divergence = dif_low2[1] > dif_low1[1]       # MACD不创新低

                return price_divergence and macd_divergence

            # 检查当前是否有金叉信号
            def check_golden_cross():
                if len(dif_clean) < 3:
                    return False

                # 当前DIF上穿DEA
                current_cross = (dif_clean[-1] > dea_clean[-1] and
                               dif_clean[-2] <= dea_clean[-2])

                # MACD在零轴下方（底部区域）
                below_zero = dif_clean[-1] < 0 and dea_clean[-1] < 0

                # HIST柱状线由负转正
                hist_turning = (hist_clean[-1] > 0 and hist_clean[-2] <= 0)

                return current_cross and below_zero and hist_turning

            # 检查成交量配合
            def check_volume_confirmation():
                try:
                    if len(self.data) < 10:
                        return True  # 数据不足时不作为否决条件

                    recent_volume = self.data[self.volume_column].iloc[-3:].mean()
                    prev_volume = self.data[self.volume_column].iloc[-10:-3].mean()

                    return recent_volume > prev_volume * 1.1  # 成交量放大
                except:
                    return True

            # 综合判断
            has_bottom_divergence = find_bottom_divergence()
            has_golden_cross = check_golden_cross()
            volume_confirmed = check_volume_confirmation()

            if has_bottom_divergence and has_golden_cross and volume_confirmed:
                return True  # 底背离买入信号

            # 检查简单的金叉信号作为备选
            if has_golden_cross and volume_confirmed:
                return True

            return False
        except Exception as e:
            print(f"MACD计算失败: {e}")
            return False

    def _cal_ma(self):
        """计算均线信号

        检查多头排列、均线斜率、成交量配合等条件，判断股票是否处于强势状态

        Returns:
            bool: 是否满足均线强势条件
        """
        try:
            if len(self.data) < 35:
                return False

            # 计算多条均线
            close = self.data[self.close_column].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()
            ma30 = close.rolling(30).mean()

            if len(ma5) < 5 or len(ma10) < 5 or len(ma30) < 5:
                return False

            # 获取最新数据
            current_price = close.iloc[-1]
            current_ma5 = ma5.iloc[-1]
            current_ma10 = ma10.iloc[-1]
            current_ma20 = ma20.iloc[-1]
            current_ma30 = ma30.iloc[-1]

            prev_ma5 = ma5.iloc[-2]
            prev_ma10 = ma10.iloc[-2]
            prev_ma30 = ma30.iloc[-2]

            # 条件1: 当前多头排列
            current_bullish = (current_price > current_ma5 > current_ma10 > current_ma30)

            # 条件2: 5日线上穿确认（从下方穿越到上方）
            ma5_cross_ma10 = (current_ma5 > current_ma10 and prev_ma5 <= prev_ma10)
            ma5_cross_ma30 = (current_ma5 > current_ma30 and prev_ma5 <= prev_ma30)

            # 至少有一个上穿信号
            has_cross_signal = ma5_cross_ma10 or ma5_cross_ma30

            # 条件3: 均线斜率向上
            ma5_slope = (current_ma5 - ma5.iloc[-3]) / ma5.iloc[-3]
            ma10_slope = (current_ma10 - ma10.iloc[-3]) / ma10.iloc[-3]

            positive_slope = ma5_slope > 0.002 and ma10_slope > 0.001  # 均线向上

            # 条件4: 价格强势确认
            # 收盘价在5日线上方且距离适中
            price_above_ma5 = current_price > current_ma5
            price_distance = (current_price - current_ma5) / current_ma5
            reasonable_distance = 0 < price_distance < 0.05  # 不能距离太远

            # 条件5: 成交量配合
            volume_support = True
            try:
                recent_volume = self.data[self.volume_column].iloc[-3:].mean()
                prev_volume = self.data[self.volume_column].iloc[-8:-3].mean()
                volume_support = recent_volume > prev_volume * 1.1
            except:
                pass

            # 条件6: 避免假突破
            # 检查前期是否有明显的下跌趋势
            trend_reversal = True
            try:
                # 20日前的价格对比
                if len(close) > 20:
                    price_20_ago = close.iloc[-20]
                    recent_low = close.iloc[-10:].min()
                    # 从低位反弹
                    trend_reversal = recent_low < price_20_ago * 0.95
            except:
                pass

            # 综合判断
            return (current_bullish and has_cross_signal and positive_slope and
                   price_above_ma5 and reasonable_distance and volume_support)
        except Exception as e:
            print(f"计算均线信号失败: {e}")
            return False
            
    def _cal_strong_trend(self):
        """计算强势趋势指标

        判断股票是否处于强势趋势，核心依据为股价上涨趋势明显且量价强势配合
        - 上涨趋势：均线系统多头排列、价格持续高于均线、整体呈上升态势
        - 量价配合：上涨时成交量放大，下跌时成交量萎缩，量价关系健康

        Returns:
            bool: 是否处于强势趋势
        """
        try:
            if len(self.data) < 20:  # 调整最小数据要求
                return False

            # 计算5日均线和10日均线
            close = self.data[self.close_column].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()

            if len(ma5) < 15:
                return False

            # 条件1: 股价持续高于均线，上涨趋势明显
            # 最近10天股价在5日线之上的比例
            recent_days = 10
            above_ma5_count = 0

            for i in range(recent_days):
                idx = -(i + 1)
                if idx < -len(close):
                    break

                price = close.iloc[idx]
                ma5_value = ma5.iloc[idx]

                # 允许轻微跌破（不超过1%）
                if price >= ma5_value * 0.99:
                    above_ma5_count += 1

            # 至少90%的时间在5日线之上
            above_ma5_ratio = above_ma5_count / min(recent_days, len(close))
            if above_ma5_ratio < 0.90:
                return False

            # 条件2: 5日均线持续上升且斜率合理
            ma5_slope_periods = 8  # 增加观察期
            ma5_uptrend_count = 0
            slope_values = []

            for i in range(1, ma5_slope_periods + 1):
                if len(ma5) > i:
                    current_ma5 = ma5.iloc[-i]
                    prev_ma5 = ma5.iloc[-(i + 1)]
                    if current_ma5 > prev_ma5:
                        ma5_uptrend_count += 1
                        # 计算斜率（日涨幅）
                        slope = (current_ma5 - prev_ma5) / prev_ma5
                        slope_values.append(slope)

            # 5日线大部分时间上升（更严格）
            ma5_uptrend_ratio = ma5_uptrend_count / ma5_slope_periods
            if ma5_uptrend_ratio < 0.75:
                return False

            # 新增条件2.1: 上涨斜率要求
            if slope_values:
                avg_slope = sum(slope_values) / len(slope_values)
                # 5日均线日均涨幅要在0.3%-2%之间（避免过缓或过急）
                if avg_slope < 0.003 or avg_slope > 0.02:
                    return False

            # 新增条件2.2: 5日线相对10日线的位置
            if len(ma10) > 5:
                current_ma5 = ma5.iloc[-1]
                current_ma10 = ma10.iloc[-1]
                # 5日线要明显高于10日线
                if current_ma5 <= current_ma10 * 1.01:
                    return False

            # 条件3: 整体上升趋势确认（更严格）
            if len(close) > 15:
                current_price = close.iloc[-1]
                price_15_ago = close.iloc[-16]
                total_gain = (current_price - price_15_ago) / price_15_ago

                # 15天涨幅要在8%-25%之间（避免涨幅过小或过大）
                if total_gain < 0.08 or total_gain > 0.25:
                    return False

            # 新增条件3.1: 价格相对均线的强度
            current_price = close.iloc[-1]
            current_ma5 = ma5.iloc[-1]
            price_ma5_ratio = current_price / current_ma5
            # 价格要适度高于5日线，但不能过度偏离
            if price_ma5_ratio < 1.005 or price_ma5_ratio > 1.08:
                return False

            # 条件4: 量价强势配合
            volume_healthy = True
            try:
                # 获取最近5天和前5天的成交量
                recent_volume = self.data[self.volume_column].iloc[-5:].mean()
                prev_volume = self.data[self.volume_column].iloc[-10:-5].mean()
                long_avg_volume = self.data[self.volume_column].iloc[-20:].mean()

                # 成交量放大比例
                volume_ratio = recent_volume / prev_volume if prev_volume > 0 else 1
                long_volume_ratio = recent_volume / long_avg_volume if long_avg_volume > 0 else 1

                # 上涨时成交量放大，下跌时成交量萎缩
                up_days = 0
                up_volume_ratio = 1.0
                down_days = 0
                down_volume_ratio = 1.0

                for i in range(5):
                    idx = -(i + 1)
                    if idx < -len(close) or idx <= -len(close) + 1:
                        break

                    today_close = close.iloc[idx]
                    yesterday_close = close.iloc[idx - 1]
                    today_volume = self.data[self.volume_column].iloc[idx]
                    yesterday_volume = self.data[self.volume_column].iloc[idx - 1]

                    if today_close > yesterday_close:
                        up_days += 1
                        if yesterday_volume > 0:
                            up_volume_ratio *= (today_volume / yesterday_volume)
                    elif today_close < yesterday_close:
                        down_days += 1
                        if yesterday_volume > 0:
                            down_volume_ratio *= (today_volume / yesterday_volume)

                # 计算平均量比
                if up_days > 0:
                    up_volume_ratio = up_volume_ratio ** (1 / up_days)
                if down_days > 0:
                    down_volume_ratio = down_volume_ratio ** (1 / down_days)

                # 成交量健康条件: 上涨时量放大，下跌时量萎缩
                volume_healthy = (volume_ratio >= 1.2 and volume_ratio <= 3.0 and
                                long_volume_ratio >= 1.2 and long_volume_ratio <= 2.5 and
                                (up_days == 0 or up_volume_ratio >= 1.1) and
                                (down_days == 0 or down_volume_ratio <= 0.9))
            except:
                volume_healthy = False

            # 条件5: 避免超涨（更严格的RSI要求）
            rsi_ok = True
            try:
                rsi = ta.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI要在合理区间，避免超买也避免过弱
                    rsi_ok = 45 <= current_rsi <= 75
            except:
                rsi_ok = False

            # 条件6: 价格形态稳健
            price_stable = True
            try:
                if len(self.data) >= 8:
                    recent_highs = self.data[self.high_column].iloc[-8:]
                    recent_lows = self.data[self.low_column].iloc[-8:]
                    recent_closes = self.data[self.close_column].iloc[-8:]

                    # 检查是否有异常跳空（更严格）
                    for i in range(1, len(recent_highs)):
                        gap_up = recent_lows.iloc[i] > recent_highs.iloc[i-1] * 1.03
                        gap_down = recent_highs.iloc[i] < recent_lows.iloc[i-1] * 0.97

                        if gap_up or gap_down:
                            price_stable = False
                            break

                    # 新增：检查价格波动的稳定性
                    if price_stable:
                        daily_changes = []
                        for i in range(1, len(recent_closes)):
                            change = abs(recent_closes.iloc[i] - recent_closes.iloc[i-1]) / recent_closes.iloc[i-1]
                            daily_changes.append(change)

                        # 单日涨跌幅不能过大
                        max_daily_change = max(daily_changes) if daily_changes else 0
                        if max_daily_change > 0.08:  # 单日涨跌幅不超过8%
                            price_stable = False
            except:
                price_stable = False

            # 新增条件7: 趋势持续性验证
            trend_consistency = True
            try:
                if len(close) >= 20:
                    # 检查最近20天是否有明显的趋势一致性
                    ma5_20_ago = ma5.iloc[-20] if len(ma5) >= 20 else ma5.iloc[0]
                    current_ma5 = ma5.iloc[-1]

                    # 5日线20天总涨幅要合理
                    ma5_total_gain = (current_ma5 - ma5_20_ago) / ma5_20_ago
                    if ma5_total_gain < 0.06 or ma5_total_gain > 0.30:  # 6%-30%区间
                        trend_consistency = False
            except:
                trend_consistency = False

            # 新增条件8: 相对强度验证
            relative_strength = True
            try:
                if len(close) >= 30:
                    # 计算相对于自身历史的强度
                    recent_avg = close.iloc[-10:].mean()
                    historical_avg = close.iloc[-30:-10].mean()

                    strength_ratio = recent_avg / historical_avg
                    # 最近表现要明显强于历史平均
                    if strength_ratio < 1.08:  # 至少强8%
                        relative_strength = False
            except:
                relative_strength = False

            return (volume_healthy and rsi_ok and price_stable and
                   trend_consistency and relative_strength)
        except Exception as e:
            print(f"计算强势趋势股失败: {e}")
            return False
            
    def _cal_breakout_platform(self):
        """计算脱离平台指标

        识别股票是否突破平台，定义为: 至少有一个波峰和波谷，突破上一个波峰，成交量温和放大

        Returns:
            bool: 是否脱离平台
        """
        try:
            if len(self.data) < 20:
                return False

            close = self.data[self.close_column].astype(float)
            high = self.data[self.high_column].astype(float)
            low = self.data[self.low_column].astype(float)
            volume = self.data[self.volume_column].astype(float)

            # 1. 识别波峰和波谷
            # 使用滚动窗口识别波峰波谷
            window_size = 5
            if len(high) < window_size * 2 + 1:
                return False

            # 波峰: 窗口内的最大值
            peaks = high.rolling(window=window_size, center=True).apply(lambda x: x[window_size//2] == x.max(), raw=True)
            # 波谷: 窗口内的最小值
            valleys = low.rolling(window=window_size, center=True).apply(lambda x: x[window_size//2] == x.min(), raw=True)

            # 转换为布尔值
            peaks = peaks.astype(bool)
            valleys = valleys.astype(bool)

            # 至少有一个波峰和一个波谷
            if not (peaks.any() and valleys.any()):
                return False

            # 2. 找到最近的波峰
            # 获取最近的波峰索引
            peak_indices = peaks[peaks].index
            if len(peak_indices) == 0:
                return False
            last_peak_idx = peak_indices[-1]
            last_peak_price = high.loc[last_peak_idx]

            # 3. 检查是否突破上一个波峰
            current_price = close.iloc[-1]
            recent_high = high.iloc[-5:].max()  # 最近5天最高价

            # 突破定义: 收盘价或最近5天最高价突破上一个波峰
            breakthrough = (current_price > last_peak_price * 1.01) or (recent_high > last_peak_price)
            if not breakthrough:
                return False

            # 4. 成交量温和放大
            # 最近5天平均成交量 vs 突破前10天平均成交量
            if len(volume) < 15:
                return False

            recent_volume_avg = volume.iloc[-5:].mean()
            pre_break_volume_avg = volume.iloc[-15:-5].mean()

            if pre_break_volume_avg == 0:
                return False

            # 成交量放大1.2-3倍视为温和放大
            volume_ratio = recent_volume_avg / pre_break_volume_avg
            if not (1.2 <= volume_ratio <= 3.0):
                return False

            # 5. 突破有效性检查
            # 突破后价格没有大幅回落
            recent_low = low.iloc[-3:].min()
            no_major_pullback = recent_low > last_peak_price * 0.95
            if not no_major_pullback:
                return False

            # 条件6: 技术指标配合
            # MACD从底部向上
            macd_support = True
            try:
                dif, dea, hist = ta.MACD(close.values, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(hist) > 5:
                    recent_hist = hist[-5:]
                    # MACD柱状线从负值向正值发展
                    hist_improving = recent_hist[-1] > recent_hist[-3]
                    macd_support = hist_improving
            except:
                pass

            # 条件7: 相对强度
            # 相对于大盘的表现
            relative_strength = True
            try:
                if len(close) > 10:
                    stock_return = (current_price - close.iloc[-11]) / close.iloc[-11]
                    # 这里简化处理，实际应该对比大盘指数
                    # 要求股票表现不能太差
                    relative_strength = stock_return > -0.1
            except:
                pass

            return macd_support and relative_strength
        except Exception as e:
            print(f"计算脱离底部平台失败: {e}")
            return False
            
    def _cal_big_divergence(self):
        """计算大分歧形态

        识别股票是否出现大分歧形态，严格定义为连续两天长上下影且一上一下

        Returns:
            bool: 是否出现大分歧形态
        """
        try:
            if len(self.data) < 3:
                return False

            # 获取最近两天的数据
            day1 = self.data.iloc[-1]  # 最新一天
            day2 = self.data.iloc[-2]  # 前一天

            # 计算影线长度
            def calc_shadow_ratios(day_data):
                """计算上下影线比例"""
                total_range = day_data[self.high_column] - day_data[self.low_column]
                if total_range <= 0:
                    return 0, 0, 0

                body_size = abs(day_data[self.close_column] - day_data[self.open_column])
                upper_shadow = day_data[self.high_column] - max(day_data[self.open_column], day_data[self.close_column])
                lower_shadow = min(day_data[self.open_column], day_data[self.close_column]) - day_data[self.low_column]

                body_ratio = body_size / total_range
                upper_shadow_ratio = upper_shadow / total_range
                lower_shadow_ratio = lower_shadow / total_range

                return body_ratio, upper_shadow_ratio, lower_shadow_ratio

            # 计算两天的影线比例
            body1, upper1, lower1 = calc_shadow_ratios(day1)
            body2, upper2, lower2 = calc_shadow_ratios(day2)

            # 条件1: 两天都有明显的长影线
            # 影线长度至少占总振幅的30%
            day1_has_long_shadow = max(upper1, lower1) >= 0.3
            day2_has_long_shadow = max(upper2, lower2) >= 0.3

            if not (day1_has_long_shadow and day2_has_long_shadow):
                return False

            # 条件2: 分歧模式识别 - 严格的一上一下
            # 模式1: 先长上影线，后长下影线
            pattern1 = (upper2 >= 0.3 and upper2 > lower2 * 1.5 and  # 前一天长上影
                       lower1 >= 0.3 and lower1 > upper1 * 1.5)      # 当天长下影

            # 模式2: 先长下影线，后长上影线
            pattern2 = (lower2 >= 0.3 and lower2 > upper2 * 1.5 and  # 前一天长下影
                       upper1 >= 0.3 and upper1 > lower1 * 1.5)      # 当天长上影

            has_divergence_pattern = pattern1 or pattern2

            if not has_divergence_pattern:
                return False

            # 条件3: 成交量配合
            # 分歧期间成交量应该放大
            volume_amplified = True
            try:
                recent_volume = self.data[self.volume_column].iloc[-2:].mean()  # 两天平均成交量
                prev_volume = self.data[self.volume_column].iloc[-7:-2].mean()  # 前5天平均成交量

                if prev_volume > 0:
                    volume_ratio = recent_volume / prev_volume
                    volume_amplified = volume_ratio >= 1.2  # 成交量放大20%以上
            except:
                pass

            # 条件4: 价格位置判断
            # 判断是在相对高位还是低位发生分歧
            position_reasonable = True
            try:
                current_price = day1[self.close_column]

                # 计算20日内的价格位置
                if len(self.data) >= 20:
                    recent_20_high = self.data[self.high_column].iloc[-20:].max()
                    recent_20_low = self.data[self.low_column].iloc[-20:].min()
                    price_position = (current_price - recent_20_low) / (recent_20_high - recent_20_low)

                    # 在相对高位(>0.65)或低位(<0.35)的分歧更有意义
                    position_reasonable = price_position > 0.65 or price_position < 0.35
            except:
                pass

            # 条件5: 避免连续分歧
            # 前面不能有太多类似的分歧形态
            no_recent_divergence = True
            try:
                if len(self.data) >= 7:
                    # 检查前5天是否有类似的长影线
                    divergence_count = 0
                    for i in range(3, 7):
                        if i < len(self.data):
                            prev_day = self.data.iloc[-i]
                            _, prev_upper, prev_lower = calc_shadow_ratios(prev_day)
                            if max(prev_upper, prev_lower) >= 0.25:
                                divergence_count += 1
                                if divergence_count > 1:
                                    no_recent_divergence = False
                                    break
            except:
                pass

            # 条件6: 技术指标确认
            # RSI在超买或超卖区域更容易形成转折
            rsi_confirmation = True
            try:
                close = self.data[self.close_column].astype(float)
                rsi = ta.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI在35以下或65以上时分歧更有效
                    rsi_confirmation = current_rsi < 38 or current_rsi > 62
            except:
                pass

            return (volume_amplified or position_reasonable or
                   no_recent_divergence or rsi_confirmation)
        except Exception as e:
            print(f"计算大分歧失败: {e}")
            return False
            
    def _cal_fund_accumulation(self):
        """计算资金吸筹指标

        多维度判断股票是否有资金吸筹行为，包括成交量变化、价格重心、换手率等指标

        Returns:
            bool: 是否存在资金吸筹
        """
        try:
            # 1. 成交量变化检查
            def check_volume_change():
                # 成交量温和放大
                volume_trend = self.data[self.volume_column].rolling(5).mean()
                volume_increase = (volume_trend.iloc[-1] > volume_trend.iloc[-10] * 1.5) & (volume_trend.iloc[-1] < volume_trend.iloc[-10] * 3)
                return volume_increase
                
            # 2. 价格重心抬升
            def check_price_center():
                price_center = (self.data[self.high_column] + self.data[self.low_column]) / 2
                center_trend = price_center.rolling(5).mean()
                center_increase = center_trend.iloc[-1] > center_trend.iloc[-10]
                return center_increase
                
            # 3. 换手率适中
            def check_turnover():
                # 估算换手率（假设流通股不变）
                turnover = self.data[self.volume_column] / self.config.get('circulating_shares', 1)
                turnover_condition = (turnover.iloc[-1] > 0.02) & (turnover.iloc[-1] < 0.15)
                return turnover_condition
                
            # 4. 技术指标底部特征
            def check_technical_bottom():
                rsi = ta.RSI(self.data[self.close_column].values, timeperiod=14)
                macd, signal, hist = ta.MACD(self.data[self.close_column].values, fastperiod=12, slowperiod=26, signalperiod=9)
                technical_bottom = (rsi[-1] < 40) & (macd[-1] < 0) & (hist[-1] > hist[-2])
                return technical_bottom
                
            # 5. 价格位置合理
            def check_price_position():
                price_range = self.data[self.high_column].rolling(60).max() - self.data[self.low_column].rolling(60).min()
                price_position = (self.data[self.close_column].iloc[-1] - self.data[self.low_column].rolling(60).min().iloc[-1]) / (price_range.iloc[-1] + 1e-9)
                position_condition = (price_position > 0.2) & (price_position < 0.5)
                return position_condition
                
            # 6. 成交金额稳定增长
            def check_amount_growth():
                amount = self.data[self.volume_column] * self.data[self.close_column]
                amount_trend = amount.rolling(5).mean()
                amount_increase = (amount_trend.iloc[-1] > amount_trend.iloc[-10] * 1.3)
                return amount_increase
                
            # 综合判断（满足至少2个条件）
            conditions = [
                check_volume_change(),
                check_price_center(),
                check_turnover(),
                check_technical_bottom(),
                check_price_position(),
                check_amount_growth()
            ]
            
            met_conditions = sum(conditions)
            return met_conditions >= 2
        except Exception as e:
            print(f"计算资金吸筹失败: {e}")
            return False

    def _cal_buy_signal(self):
        """计算买入信号

        根据四个量化指标（强势趋势、平台突破、大分歧、资金吸筹）计算买入信号
        当满足多个条件时产生买入信号

        Returns:
            bool: 是否产生买入信号
        """
        try:
            # 获取四个量化指标的值
            is_strong_trend = self._cal_strong_trend()
            is_breakout_platform = self._cal_breakout_platform()
            is_big_divergence = self._cal_big_divergence()
            is_fund_accumulation = self._cal_fund_accumulation()

            # 计算满足的条件数量
            conditions_met = sum([
                is_strong_trend,
                is_breakout_platform,
                is_big_divergence,
                is_fund_accumulation
            ])

            # 当满足至少两个条件时产生买入信号
            return conditions_met >= 2
        except Exception as e:
            print(f"计算买入信号失败: {e}")
            return False