#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试强势趋势算法

分析为什么强势趋势信号没有触发
"""

import sys
import os
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from stock_old_analyzer import StockOldAnalyzer

def generate_strong_bullish_data(days=100):
    """生成强势上涨的测试数据"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    base_price = 10.0
    prices = [base_price]
    
    np.random.seed(42)
    
    # 前30天：横盘整理
    for i in range(1, 31):
        change = np.random.uniform(-0.01, 0.01)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 中间40天：强势上涨
    for i in range(31, 71):
        direction = 1 if np.random.random() < 0.8 else -1
        if direction > 0:
            change = np.random.uniform(0.02, 0.06)
        else:
            change = direction * np.random.uniform(0.01, 0.02)
        
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 最后30天：继续上涨但放缓
    for i in range(71, days):
        direction = 1 if np.random.random() < 0.65 else -1
        change = direction * np.random.uniform(0.01, 0.03)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.005, 0.02)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.uniform(-0.01, 0.01))
        
        close_price = price
        
        # 成交量：上涨时明显放量
        if i > 0:
            price_change = (close_price - prices[i-1]) / prices[i-1]
            base_volume = 1000000
            if price_change > 0.02:
                volume = base_volume * (2 + price_change * 10)
            elif price_change > 0:
                volume = base_volume * (1.2 + price_change * 5)
            else:
                volume = base_volume * (0.8 + price_change * 2)
        else:
            volume = 1000000
        
        data.append({
            'date': dates[i],
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': int(max(volume, 100000))
        })
    
    return pd.DataFrame(data)

def debug_strong_trend():
    """调试强势趋势算法"""
    print("=== 调试强势趋势算法 ===")
    
    data = generate_strong_bullish_data(100)
    analyzer = StockOldAnalyzer(data)
    
    print(f"数据长度: {len(data)}")
    print(f"总涨幅: {(data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100:.2f}%")
    
    # 手动执行强势趋势算法的每个步骤
    close = data['close'].astype(float)
    ma5 = close.rolling(5).mean()
    ma10 = close.rolling(10).mean()
    
    print(f"\n=== 条件1: 股价持续高于均线 ===")
    recent_days = 10
    above_ma5_count = 0
    
    for i in range(recent_days):
        idx = -(i + 1)
        if idx < -len(close):
            break
        
        price = close.iloc[idx]
        ma5_value = ma5.iloc[idx]
        
        if price >= ma5_value * 0.99:
            above_ma5_count += 1
        
        print(f"第{i+1}天前: 价格={price:.2f}, MA5={ma5_value:.2f}, 满足条件={'是' if price >= ma5_value * 0.99 else '否'}")
    
    above_ma5_ratio = above_ma5_count / min(recent_days, len(close))
    print(f"满足条件的天数: {above_ma5_count}/{recent_days}")
    print(f"比例: {above_ma5_ratio:.2%} (要求: ≥90%)")
    print(f"条件1通过: {above_ma5_ratio >= 0.90}")
    
    print(f"\n=== 条件2: 5日均线持续上升 ===")
    ma5_slope_periods = 8
    ma5_uptrend_count = 0
    slope_values = []
    
    for i in range(1, ma5_slope_periods + 1):
        if len(ma5) > i:
            current_ma5 = ma5.iloc[-i]
            prev_ma5 = ma5.iloc[-(i + 1)]
            if current_ma5 > prev_ma5:
                ma5_uptrend_count += 1
                slope = (current_ma5 - prev_ma5) / prev_ma5
                slope_values.append(slope)
                print(f"第{i}天: MA5={current_ma5:.2f}, 前一天MA5={prev_ma5:.2f}, 斜率={slope:.4f}, 上升={'是' if current_ma5 > prev_ma5 else '否'}")
            else:
                print(f"第{i}天: MA5={current_ma5:.2f}, 前一天MA5={prev_ma5:.2f}, 上升={'否'}")
    
    ma5_uptrend_ratio = ma5_uptrend_count / ma5_slope_periods
    print(f"上升天数: {ma5_uptrend_count}/{ma5_slope_periods}")
    print(f"上升比例: {ma5_uptrend_ratio:.2%} (要求: ≥75%)")
    
    if slope_values:
        avg_slope = sum(slope_values) / len(slope_values)
        print(f"平均斜率: {avg_slope:.4f} (要求: 0.003-0.02)")
        slope_ok = 0.003 <= avg_slope <= 0.02
    else:
        avg_slope = 0
        slope_ok = False
    
    print(f"条件2通过: {ma5_uptrend_ratio >= 0.75 and slope_ok}")
    
    print(f"\n=== 条件3: 整体上升趋势确认 ===")
    if len(close) > 15:
        current_price = close.iloc[-1]
        price_15_ago = close.iloc[-16]
        total_gain = (current_price - price_15_ago) / price_15_ago
        print(f"当前价格: {current_price:.2f}")
        print(f"15天前价格: {price_15_ago:.2f}")
        print(f"15天涨幅: {total_gain:.2%} (要求: 5%-50%)")
        condition3_ok = 0.05 <= total_gain <= 0.50
    else:
        condition3_ok = False
        total_gain = 0
    
    print(f"条件3通过: {condition3_ok}")
    
    print(f"\n=== 条件4: 价格相对均线的强度 ===")
    current_price = close.iloc[-1]
    current_ma5 = ma5.iloc[-1]
    price_ma5_ratio = current_price / current_ma5
    print(f"当前价格: {current_price:.2f}")
    print(f"当前MA5: {current_ma5:.2f}")
    print(f"价格/MA5比例: {price_ma5_ratio:.4f} (要求: 1.005-1.08)")
    condition4_ok = 1.005 <= price_ma5_ratio <= 1.08
    print(f"条件4通过: {condition4_ok}")
    
    print(f"\n=== 条件5: 量价强势配合 ===")
    try:
        recent_volume = data['volume'].iloc[-5:].mean()
        prev_volume = data['volume'].iloc[-10:-5].mean()
        long_avg_volume = data['volume'].iloc[-20:].mean()
        
        volume_ratio = recent_volume / prev_volume if prev_volume > 0 else 1
        long_volume_ratio = recent_volume / long_avg_volume if long_avg_volume > 0 else 1
        
        print(f"最近5天平均成交量: {recent_volume:.0f}")
        print(f"前5天平均成交量: {prev_volume:.0f}")
        print(f"最近20天平均成交量: {long_avg_volume:.0f}")
        print(f"成交量比例(近5天/前5天): {volume_ratio:.2f} (要求: 1.2-3.0)")
        print(f"成交量比例(近5天/近20天): {long_volume_ratio:.2f} (要求: 1.2-2.5)")
        
        volume_healthy = (1.2 <= volume_ratio <= 3.0 and 1.2 <= long_volume_ratio <= 2.5)
    except:
        volume_healthy = False
    
    print(f"条件5通过: {volume_healthy}")
    
    print(f"\n=== 最终结果 ===")
    final_result = analyzer._cal_strong_trend()
    print(f"强势趋势信号: {final_result}")

if __name__ == "__main__":
    debug_strong_trend()
