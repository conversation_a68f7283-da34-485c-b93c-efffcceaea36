#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的StockOldAnalyzer测试脚本

测试优化后的算法准确度和买卖点胜率
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, '..', '..'))

from stock_old_analyzer import StockOldAnalyzer, AnalysisConfig

def generate_test_data(days=100, trend='bullish'):
    """生成测试数据"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    # 基础价格
    base_price = 10.0
    prices = [base_price]
    volumes = []
    
    np.random.seed(42)  # 确保可重复性
    
    for i in range(1, days):
        # 根据趋势类型生成价格
        if trend == 'bullish':
            # 上涨趋势：70%概率上涨
            direction = 1 if np.random.random() < 0.7 else -1
            change = direction * np.random.uniform(0.01, 0.05)
        elif trend == 'bearish':
            # 下跌趋势：70%概率下跌
            direction = -1 if np.random.random() < 0.7 else 1
            change = direction * np.random.uniform(0.01, 0.05)
        else:  # sideways
            # 横盘：随机波动
            direction = 1 if np.random.random() < 0.5 else -1
            change = direction * np.random.uniform(0.005, 0.02)
        
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 防止价格为负
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        # 生成开高低收
        volatility = np.random.uniform(0.01, 0.03)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        
        if i == 0:
            open_price = price
        else:
            # 开盘价接近前一日收盘价
            open_price = prices[i-1] * (1 + np.random.uniform(-0.02, 0.02))
        
        close_price = price
        
        # 成交量：价格上涨时放量
        if i > 0:
            price_change = (close_price - prices[i-1]) / prices[i-1]
            base_volume = 1000000
            if price_change > 0:
                volume = base_volume * (1 + price_change * 5)  # 上涨放量
            else:
                volume = base_volume * (1 + price_change * 2)  # 下跌缩量
        else:
            volume = 1000000
        
        data.append({
            'date': dates[i],
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': int(volume)
        })
    
    return pd.DataFrame(data)

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 生成测试数据
    test_data = generate_test_data(60, 'bullish')
    
    # 创建分析器
    config = {
        'rsi_period': 14,
        'volume_threshold': 1.5,
        'momentum_threshold': 0.002
    }
    analyzer = StockOldAnalyzer(test_data, config)
    
    # 执行分析
    result = analyzer.analyze_stock('TEST001')
    
    print(f"股票代码: {result.code}")
    print(f"10日收益率: {result.ret10:.4f}")
    print(f"20日收益率: {result.ret20:.4f}")
    print(f"动量信号: {result.momentum}")
    print(f"MACD信号: {result.macd}")
    print(f"均线信号: {result.ma}")
    print(f"买入信号: {result.buySignal}")
    print(f"技术面评分: {result.score2}")
    print(f"资金面评分: {result.score3}")
    
    return result

def test_different_market_conditions():
    """测试不同市场条件下的表现"""
    print("\n=== 测试不同市场条件 ===")
    
    conditions = ['bullish', 'bearish', 'sideways']
    results = {}
    
    for condition in conditions:
        print(f"\n--- {condition.upper()} 市场 ---")
        test_data = generate_test_data(80, condition)
        analyzer = StockOldAnalyzer(test_data)
        result = analyzer.analyze_stock(f'TEST_{condition.upper()}')
        
        results[condition] = result
        
        print(f"买入信号: {result.buySignal}")
        print(f"强势趋势: {result.strongTrend}")
        print(f"平台突破: {result.breakoutPlatform}")
        print(f"技术面评分: {result.score2}")
        
    return results

def test_signal_accuracy():
    """测试信号准确度"""
    print("\n=== 测试信号准确度 ===")
    
    # 生成多个测试样本
    test_cases = []
    for i in range(10):
        # 随机选择市场条件
        condition = np.random.choice(['bullish', 'bearish', 'sideways'])
        test_data = generate_test_data(100, condition)
        
        analyzer = StockOldAnalyzer(test_data)
        result = analyzer.analyze_stock(f'TEST_{i:03d}')
        
        test_cases.append({
            'code': result.code,
            'condition': condition,
            'buy_signal': result.buySignal,
            'momentum': result.momentum,
            'macd': result.macd,
            'ma': result.ma,
            'ret10': result.ret10,
            'score2': result.score2
        })
    
    # 分析结果
    df_results = pd.DataFrame(test_cases)
    
    print("信号统计:")
    print(f"总样本数: {len(df_results)}")
    print(f"买入信号数: {df_results['buy_signal'].sum()}")
    print(f"买入信号比例: {df_results['buy_signal'].mean():.2%}")
    
    # 按市场条件分组
    for condition in ['bullish', 'bearish', 'sideways']:
        subset = df_results[df_results['condition'] == condition]
        if len(subset) > 0:
            print(f"{condition} 市场买入信号比例: {subset['buy_signal'].mean():.2%}")
    
    return df_results

def test_performance_metrics():
    """测试性能指标"""
    print("\n=== 测试性能指标 ===")
    
    # 测试计算速度
    import time
    
    test_data = generate_test_data(200, 'bullish')
    analyzer = StockOldAnalyzer(test_data)
    
    start_time = time.time()
    for i in range(10):
        result = analyzer.analyze_stock(f'PERF_{i:03d}')
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"平均分析时间: {avg_time:.4f} 秒")
    
    # 测试内存使用
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    memory_before = process.memory_info().rss / 1024 / 1024  # MB
    
    # 创建多个分析器实例
    analyzers = []
    for i in range(5):
        data = generate_test_data(150, 'bullish')
        analyzers.append(StockOldAnalyzer(data))
    
    memory_after = process.memory_info().rss / 1024 / 1024  # MB
    memory_usage = memory_after - memory_before
    
    print(f"内存使用增量: {memory_usage:.2f} MB")
    
    return avg_time, memory_usage

def main():
    """主测试函数"""
    print("优化后的StockOldAnalyzer测试")
    print("=" * 50)
    
    try:
        # 基本功能测试
        basic_result = test_basic_functionality()
        
        # 不同市场条件测试
        market_results = test_different_market_conditions()
        
        # 信号准确度测试
        accuracy_results = test_signal_accuracy()
        
        # 性能测试
        avg_time, memory_usage = test_performance_metrics()
        
        print("\n=== 测试总结 ===")
        print("✅ 基本功能测试通过")
        print("✅ 市场条件测试通过")
        print("✅ 信号准确度测试通过")
        print("✅ 性能测试通过")
        print(f"平均处理时间: {avg_time:.4f}秒")
        print(f"内存使用: {memory_usage:.2f}MB")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
