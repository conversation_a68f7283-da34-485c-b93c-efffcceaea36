#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试优化后的StockOldAnalyzer

分析各个技术指标的表现和买入信号的触发条件
"""

import sys
import os
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from stock_old_analyzer import StockOldAnalyzer, AnalysisConfig

def generate_strong_bullish_data(days=100):
    """生成强势上涨的测试数据，更容易触发买入信号"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    base_price = 10.0
    prices = [base_price]
    
    np.random.seed(42)
    
    # 前30天：横盘整理，为后续突破做准备
    for i in range(1, 31):
        change = np.random.uniform(-0.01, 0.01)  # 小幅波动
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 中间40天：强势上涨，模拟突破行情
    for i in range(31, 71):
        # 80%概率上涨，涨幅较大
        direction = 1 if np.random.random() < 0.8 else -1
        if direction > 0:
            change = np.random.uniform(0.02, 0.06)  # 2%-6%上涨
        else:
            change = direction * np.random.uniform(0.01, 0.02)  # 小幅回调
        
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 最后30天：继续上涨但放缓
    for i in range(71, days):
        direction = 1 if np.random.random() < 0.65 else -1
        change = direction * np.random.uniform(0.01, 0.03)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.005, 0.02)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.uniform(-0.01, 0.01))
        
        close_price = price
        
        # 成交量：上涨时明显放量
        if i > 0:
            price_change = (close_price - prices[i-1]) / prices[i-1]
            base_volume = 1000000
            if price_change > 0.02:  # 大涨时放量
                volume = base_volume * (2 + price_change * 10)
            elif price_change > 0:  # 小涨时温和放量
                volume = base_volume * (1.2 + price_change * 5)
            else:  # 下跌时缩量
                volume = base_volume * (0.8 + price_change * 2)
        else:
            volume = 1000000
        
        data.append({
            'date': dates[i],
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': int(max(volume, 100000))  # 最小成交量
        })
    
    return pd.DataFrame(data)

def test_individual_indicators():
    """测试各个技术指标的表现"""
    print("=== 测试各个技术指标 ===")
    
    # 生成强势上涨数据
    test_data = generate_strong_bullish_data(100)
    analyzer = StockOldAnalyzer(test_data)
    
    print("数据概览:")
    print(f"总天数: {len(test_data)}")
    print(f"起始价格: {test_data['close'].iloc[0]:.2f}")
    print(f"结束价格: {test_data['close'].iloc[-1]:.2f}")
    print(f"总涨幅: {(test_data['close'].iloc[-1] / test_data['close'].iloc[0] - 1) * 100:.2f}%")
    print(f"平均成交量: {test_data['volume'].mean():.0f}")
    
    print("\n各项技术指标测试:")
    
    # 测试基础指标
    print(f"连板数: {analyzer._cal_limit_num()}")
    print(f"10日动量: {analyzer._cal_price_momentum(10):.4f}")
    print(f"20日动量: {analyzer._cal_price_momentum(20):.4f}")
    print(f"动量信号: {analyzer._cal_momentum()}")
    print(f"三连阳: {analyzer._cal_isTup()}")
    print(f"K线形态: {analyzer._cal_k()}")
    
    # 测试复杂指标
    print(f"底部反转: {analyzer._cal_bottom_inversion()}")
    print(f"放量突破: {analyzer._cal_isHeavyVolume()}")
    print(f"MACD信号: {analyzer._cal_macd()}")
    print(f"均线信号: {analyzer._cal_ma()}")
    print(f"EMV信号: {analyzer._cal_emv()}")
    print(f"收盘接近最高: {analyzer._cal_high_max()}")
    
    # 测试高级信号
    print(f"强势趋势: {analyzer._cal_strong_trend()}")
    print(f"平台突破: {analyzer._cal_breakout_platform()}")
    print(f"大分歧: {analyzer._cal_big_divergence()}")
    print(f"资金吸筹: {analyzer._cal_fund_accumulation()}")
    
    # 风险检查
    print(f"风险检查通过: {analyzer._perform_risk_checks()}")
    print(f"市场环境评分: {analyzer._assess_market_condition():.2f}")
    
    return analyzer

def test_signal_sensitivity():
    """测试信号敏感度"""
    print("\n=== 测试信号敏感度 ===")
    
    # 测试不同强度的上涨行情
    scenarios = [
        ("温和上涨", 0.02, 0.04),  # 2%-4%日涨幅
        ("强势上涨", 0.03, 0.06),  # 3%-6%日涨幅
        ("暴涨行情", 0.05, 0.08),  # 5%-8%日涨幅
    ]
    
    for scenario_name, min_gain, max_gain in scenarios:
        print(f"\n--- {scenario_name} ---")
        
        # 生成特定强度的数据
        data = generate_custom_trend_data(60, min_gain, max_gain)
        analyzer = StockOldAnalyzer(data)
        result = analyzer.analyze_stock(f'TEST_{scenario_name}')
        
        print(f"买入信号: {result.buySignal}")
        print(f"强势趋势: {result.strongTrend}")
        print(f"平台突破: {result.breakoutPlatform}")
        print(f"技术面评分: {result.score2}")
        print(f"总涨幅: {(data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100:.1f}%")

def generate_custom_trend_data(days, min_gain, max_gain):
    """生成自定义趋势强度的数据"""
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    base_price = 10.0
    prices = [base_price]
    
    np.random.seed(42)
    
    for i in range(1, days):
        # 70%概率上涨
        if np.random.random() < 0.7:
            change = np.random.uniform(min_gain, max_gain)
        else:
            change = -np.random.uniform(0.01, 0.02)  # 小幅回调
        
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.01, 0.03)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        
        open_price = prices[i-1] * (1 + np.random.uniform(-0.01, 0.01)) if i > 0 else price
        close_price = price
        
        # 成交量配合
        if i > 0:
            price_change = (close_price - prices[i-1]) / prices[i-1]
            base_volume = 1000000
            volume = base_volume * (1 + price_change * 8)
        else:
            volume = 1000000
        
        data.append({
            'date': dates[i],
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': int(max(volume, 100000))
        })
    
    return pd.DataFrame(data)

def analyze_signal_thresholds():
    """分析信号阈值的合理性"""
    print("\n=== 分析信号阈值 ===")
    
    # 生成强势数据
    data = generate_strong_bullish_data(80)
    analyzer = StockOldAnalyzer(data)
    
    # 分析各个组件的评分
    isStrongTrend = analyzer._cal_strong_trend()
    isBreakoutPlatform = analyzer._cal_breakout_platform()
    isBigDivergence = analyzer._cal_big_divergence()
    isFundAccumulation = analyzer._cal_fund_accumulation()
    momentum = analyzer._cal_momentum()
    macd = analyzer._cal_macd()
    ma = analyzer._cal_ma()
    emv = analyzer._cal_emv()
    
    # 计算加权评分
    signal_weights = analyzer.config.signal_weights
    weighted_score = (
        signal_weights['strong_trend'] * isStrongTrend +
        signal_weights['breakout_platform'] * isBreakoutPlatform +
        signal_weights['big_divergence'] * isBigDivergence +
        signal_weights['fund_accumulation'] * isFundAccumulation
    )
    
    auxiliary_signals = [momentum, macd, ma, emv]
    auxiliary_score = sum(auxiliary_signals) / len(auxiliary_signals)
    
    market_condition = analyzer._assess_market_condition()
    
    comprehensive_score = (
        weighted_score * 0.7 + 
        auxiliary_score * 0.2 + 
        market_condition * 0.1
    )
    
    print("信号组件分析:")
    print(f"强势趋势: {isStrongTrend} (权重: {signal_weights['strong_trend']})")
    print(f"平台突破: {isBreakoutPlatform} (权重: {signal_weights['breakout_platform']})")
    print(f"大分歧: {isBigDivergence} (权重: {signal_weights['big_divergence']})")
    print(f"资金吸筹: {isFundAccumulation} (权重: {signal_weights['fund_accumulation']})")
    print(f"加权主要信号评分: {weighted_score:.3f}")
    
    print(f"\n辅助信号:")
    print(f"动量: {momentum}, MACD: {macd}, 均线: {ma}, EMV: {emv}")
    print(f"辅助信号评分: {auxiliary_score:.3f}")
    
    print(f"\n市场环境评分: {market_condition:.3f}")
    print(f"综合评分: {comprehensive_score:.3f}")
    print(f"当前阈值: 0.6")
    print(f"是否触发买入: {comprehensive_score >= 0.6}")

def main():
    """主测试函数"""
    print("详细测试优化后的StockOldAnalyzer")
    print("=" * 50)
    
    try:
        # 测试各个指标
        analyzer = test_individual_indicators()
        
        # 测试信号敏感度
        test_signal_sensitivity()
        
        # 分析阈值合理性
        analyze_signal_thresholds()
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试完成，请查看上述结果分析")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
